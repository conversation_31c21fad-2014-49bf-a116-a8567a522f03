# Automatic App Restart Solution

## Problem
When a React Native app is put in the background for extended periods (2+ hours), it often displays a white screen or crashes when brought back to the foreground. This is caused by:

1. **Memory corruption** - App's memory state becomes corrupted during long background periods
2. **Navigation state corruption** - React Navigation state gets corrupted
3. **Apollo client issues** - GraphQL client connections become stale
4. **Stream chat client issues** - Chat client connections become invalid
5. **Cache corruption** - AsyncStorage and other cached data becomes inconsistent

## Solution Overview

This solution implements **automatic app restart** without any user interaction when the app has been in the background for more than 2 hours.

### Key Features:
- ✅ **Automatic restart** - No popups or user interaction required
- ✅ **Production-ready thresholds** - 2 hours background time before restart
- ✅ **Cache clearing** - Clears corrupted cache before restart
- ✅ **Proper restart** - Uses `react-native-restart` for clean app restart
- ✅ **Background persistence** - Remembers background time across app kills
- ✅ **Error handling** - Fallback restart if cache clearing fails

## Implementation Details

### 1. AppStateManager (`src/utils/AppStateManager.js`)
- **Background threshold**: 2 hours (7,200,000 ms)
- **Automatic restart**: Uses `RNRestart.Restart()` from `react-native-restart`
- **Cache clearing**: Removes corrupted navigation, Apollo, and Stream chat cache
- **No user interaction**: Completely silent restart process

### 2. useAppReload Hook (`src/hooks/useAppReload.js`)
- **Silent operation**: No alerts or popups shown to user
- **Automatic execution**: Triggers restart immediately when needed
- **Cache management**: Clears app state before restart
- **Error handling**: Direct restart if cache clearing fails

### 3. App.tsx Integration
- **Removed AppReloadOverlay**: No UI components needed for automatic restart
- **useEffect trigger**: Automatically calls restart when needed
- **Clean integration**: Minimal changes to existing app structure

## How It Works

1. **Background Detection**: App detects when it goes to background and records timestamp
2. **Foreground Check**: When app comes to foreground, calculates background duration
3. **Threshold Check**: If background time > 2 hours, triggers automatic restart
4. **Cache Clearing**: Removes corrupted cache data (navigation, Apollo, Stream chat)
5. **App Restart**: Uses `RNRestart.Restart()` to cleanly restart the app
6. **Fresh Start**: App starts fresh as if opened for the first time

## Configuration

### Production Settings (Current)
```javascript
this.backgroundThreshold = 2 * 60 * 60 * 1000; // 2 hours
this.forceReloadThreshold = 2 * 60 * 60 * 1000; // 2 hours
```

### Testing Settings (Available in AppStateManagerTest.js)
```javascript
this.backgroundThreshold = 30 * 1000; // 30 seconds
this.forceReloadThreshold = 30 * 1000; // 30 seconds
```

## Testing

### For Development Testing:
1. Replace `AppStateManager` import with `AppStateManagerTest` in `useAppReload.js`
2. Put app in background for 30+ seconds
3. Bring app to foreground
4. App should automatically restart without any popup

### For Production:
- App will automatically restart after 2+ hours in background
- No user interaction required
- Completely transparent to the user

## Files Modified

1. **`src/utils/AppStateManager.js`** - Updated for automatic restart
2. **`src/hooks/useAppReload.js`** - Removed alerts, added automatic restart
3. **`App.tsx`** - Removed AppReloadOverlay, added automatic trigger
4. **`package.json`** - Added `react-native-restart` dependency

## Files Created

1. **`src/utils/AppStateManagerTest.js`** - Test version with 30-second threshold
2. **`AUTOMATIC_RESTART_SOLUTION.md`** - This documentation

## Benefits

- ✅ **No white screens** - App restarts before corruption causes issues
- ✅ **No user interruption** - Completely automatic and silent
- ✅ **Better performance** - Fresh app state eliminates memory leaks
- ✅ **Improved reliability** - Prevents crashes from corrupted state
- ✅ **Production ready** - 2-hour threshold is reasonable for real usage

## Usage

The solution is now active and requires no additional setup. The app will automatically restart when needed without any user interaction.

To test with shorter intervals, temporarily replace the import in `useAppReload.js`:

```javascript
// For testing (30 seconds)
import appStateManager from '../utils/AppStateManagerTest';

// For production (2 hours)
import appStateManager from '../utils/AppStateManager';
```
