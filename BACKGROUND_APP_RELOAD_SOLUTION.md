# Background App Reload Solution

## Problem Description

When your React Native app is put in the background for more than a few hours and then brought back to the foreground, it often displays a white screen or crashes immediately. This is a common issue caused by:

1. **Memory corruption**: The app's memory state becomes corrupted during long background periods
2. **Navigation state corruption**: React Navigation state gets corrupted
3. **Apollo client issues**: GraphQL client connections become stale
4. **Stream chat client issues**: Chat client connections become invalid
5. **Cache corruption**: AsyncStorage and other cached data becomes inconsistent

## Solution Overview

This solution implements a comprehensive app lifecycle management system that:

1. **Monitors app state changes** using React Native's AppState API
2. **Tracks background duration** to determine when a reload is needed
3. **Automatically triggers app reload** when returning from long background periods
4. **Provides user-friendly reload experience** with clear messaging
5. **Handles crashes gracefully** with enhanced error boundaries

## Components

### 1. AppStateManager (`src/utils/AppStateManager.js`)

**Purpose**: Centralized app state management and background duration tracking

**Key Features**:
- Tracks when app goes to background/foreground
- Measures background duration
- Automatically triggers reload after 2+ hours in background
- Suggests reload after 30+ minutes in background
- Clears critical cache data during reload

**Configuration**:
```javascript
this.backgroundThreshold = 30 * 60 * 1000; // 30 minutes
this.forceReloadThreshold = 2 * 60 * 60 * 1000; // 2 hours
```

### 2. useAppReload Hook (`src/hooks/useAppReload.js`)

**Purpose**: React hook for managing app reload state and process

**Key Features**:
- Integrates with AppStateManager
- Handles force reload process
- Clears app state and cache
- Manages reload UI state

### 3. AppReloadOverlay (`src/components/AppReloadOverlay.js`)

**Purpose**: User interface for app reload process

**Key Features**:
- Modal overlay for reload messaging
- Loading state during reload
- User-friendly messaging
- Consistent with app design

### 4. Enhanced ErrorBoundary (`src/components/ErrorBoundry/ErrorBoundry.js`)

**Purpose**: Handles app crashes and provides recovery options

**Key Features**:
- Catches JavaScript errors
- Provides reload option
- Error reporting functionality
- Debug information in development

## How It Works

### 1. App State Monitoring

```javascript
// AppStateManager tracks app state changes
AppState.addEventListener('change', (nextAppState) => {
    if (this.appState.match(/inactive|background/) && nextAppState === 'active') {
        this.handleAppForeground();
    } else if (nextAppState.match(/inactive|background/)) {
        this.handleAppBackground();
    }
});
```

### 2. Background Duration Tracking

```javascript
// When app goes to background
handleAppBackground() {
    this.backgroundStartTime = Date.now();
    this.saveBackgroundTime();
}

// When app comes to foreground
handleAppForeground() {
    const backgroundDuration = Date.now() - this.backgroundStartTime;
    
    if (backgroundDuration > this.forceReloadThreshold) {
        await this.forceAppReload();
    }
}
```

### 3. Cache Clearing During Reload

```javascript
// Clear critical cache data
const keysToClear = [
    'navigation_state',
    'apollo_cache', 
    'stream_chat_state',
    'temp_data'
];

// Clear Apollo client cache
if (global.apolloClient) {
    await global.apolloClient.clearStore();
}

// Disconnect Stream chat client
if (global.streamClient) {
    await global.streamClient.disconnectUser();
}
```

## Implementation Steps

### 1. Files Created/Modified

- ✅ `src/utils/AppStateManager.js` - New file
- ✅ `src/hooks/useAppReload.js` - New file  
- ✅ `src/components/AppReloadOverlay.js` - New file
- ✅ `src/components/ErrorBoundry/ErrorBoundry.js` - Enhanced
- ✅ `App.tsx` - Updated to integrate reload functionality

### 2. Integration Points

**Main App Component**:
```javascript
// App.tsx
const { isReloading, shouldForceReload, handleForceReload } = useAppReload();

// Don't render main app if reload is needed
if (shouldForceReload) {
    return <AppReloadOverlay visible={shouldForceReload} onReload={handleForceReload} />;
}
```

**Global Client Storage**:
```javascript
// Store clients globally for reload access
useEffect(() => {
    if (client) {
        (global as any).apolloClient = client;
    }
}, [client]);
```

## Configuration Options

### Timing Thresholds

You can adjust the timing thresholds in `AppStateManager.js`:

```javascript
// Suggest reload after 30 minutes in background
this.backgroundThreshold = 30 * 60 * 1000;

// Force reload after 2 hours in background  
this.forceReloadThreshold = 2 * 60 * 60 * 1000;
```

### Cache Keys to Clear

Modify the cache keys cleared during reload in `useAppReload.js`:

```javascript
const keysToKeep = [
    'user_token',
    'user_data', 
    'auth_state',
    'background_start_time'
];
```

## User Experience

### 1. Automatic Detection
- App automatically detects when it's been in background too long
- No user intervention required for detection

### 2. User-Friendly Messaging
- Clear explanation of why reload is needed
- Professional UI consistent with app design
- Loading indicators during reload process

### 3. Graceful Error Handling
- Enhanced error boundary catches crashes
- Provides reload option when errors occur
- Error reporting for debugging

## Testing

### Manual Testing

1. **Background Duration Test**:
   - Put app in background for 2+ hours
   - Bring app to foreground
   - Verify reload overlay appears

2. **Crash Recovery Test**:
   - Simulate app crash
   - Verify error boundary catches error
   - Test reload functionality

3. **Cache Clearing Test**:
   - Verify critical cache is cleared during reload
   - Check that authentication data is preserved

### Debug Information

In development mode, the error boundary shows debug information:
- Error stack traces
- Error details
- Platform and version information

## Benefits

1. **Prevents White Screen Issues**: Automatic reload prevents corrupted state
2. **Improves App Stability**: Reduces crashes from background state corruption
3. **Better User Experience**: Clear messaging and smooth reload process
4. **Maintains Data Integrity**: Preserves authentication while clearing problematic cache
5. **Easy to Maintain**: Centralized solution with clear configuration

## Troubleshooting

### Common Issues

1. **App still shows white screen after reload**:
   - Check if all cache keys are being cleared
   - Verify Apollo and Stream clients are properly reset

2. **Reload not triggering**:
   - Check AppStateManager initialization
   - Verify background duration tracking

3. **Authentication lost after reload**:
   - Ensure auth-related keys are in `keysToKeep` array
   - Check auth state restoration logic

### Debug Logs

Enable debug logging by checking console output:
- AppStateManager logs background duration
- useAppReload logs cache clearing process
- ErrorBoundary logs caught errors

## Future Enhancements

1. **Smart Reload**: Only reload specific components instead of full app
2. **Background Sync**: Sync data before going to background
3. **Predictive Reload**: Reload before user returns based on usage patterns
4. **Customizable Thresholds**: User-configurable reload timing
5. **Analytics Integration**: Track reload frequency and success rates 