import React from 'react';
import {
    View,
    Text,
    StyleSheet,
    ActivityIndicator,
    TouchableOpacity,
    Dimensions,
    Modal
} from 'react-native';
import { colors } from '../theme/theme';

const { width, height } = Dimensions.get('window');

const AppReloadOverlay = ({ 
    visible, 
    onReload, 
    isReloading = false,
    message = "The app needs to reload after being in the background for 1+ minute (TESTING MODE)."
}) => {
    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="fade"
            statusBarTranslucent={true}
        >
            <View style={styles.overlay}>
                <View style={styles.container}>
                    <View style={styles.iconContainer}>
                        <ActivityIndicator 
                            size="large" 
                            color={colors.primary} 
                            animating={isReloading}
                        />
                    </View>
                    
                    <Text style={styles.title}>
                        {isReloading ? 'Reloading App...' : 'App Reload Required (TESTING)'}
                    </Text>
                    
                    <Text style={styles.message}>
                        {message}
                    </Text>
                    
                    {!isReloading && (
                        <TouchableOpacity 
                            style={styles.reloadButton}
                            onPress={onReload}
                            activeOpacity={0.8}
                        >
                            <Text style={styles.reloadButtonText}>
                                Reload Now
                            </Text>
                        </TouchableOpacity>
                    )}
                    
                    {isReloading && (
                        <Text style={styles.loadingText}>
                            Please wait while we reload the app...
                        </Text>
                    )}

                    <View style={styles.testingInfo}>
                        <Text style={styles.testingText}>
                            🧪 Testing Mode: 1-minute background threshold
                        </Text>
                    </View>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    container: {
        backgroundColor: colors.white,
        borderRadius: 16,
        padding: 24,
        margin: 20,
        alignItems: 'center',
        maxWidth: width * 0.85,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    iconContainer: {
        width: 60,
        height: 60,
        borderRadius: 30,
        backgroundColor: colors.lightGray,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 16,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: colors.text,
        textAlign: 'center',
        marginBottom: 12,
    },
    message: {
        fontSize: 16,
        color: colors.textSecondary,
        textAlign: 'center',
        lineHeight: 22,
        marginBottom: 24,
    },
    reloadButton: {
        backgroundColor: colors.primary,
        paddingHorizontal: 32,
        paddingVertical: 12,
        borderRadius: 8,
        minWidth: 120,
    },
    reloadButtonText: {
        color: colors.white,
        fontSize: 16,
        fontWeight: '600',
        textAlign: 'center',
    },
    loadingText: {
        fontSize: 14,
        color: colors.textSecondary,
        textAlign: 'center',
        fontStyle: 'italic',
    },
    testingInfo: {
        marginTop: 16,
        padding: 8,
        backgroundColor: '#FFF3CD',
        borderRadius: 6,
        borderWidth: 1,
        borderColor: '#FFEAA7',
    },
    testingText: {
        fontSize: 12,
        color: '#856404',
        textAlign: 'center',
        fontStyle: 'italic',
    },
});

export default AppReloadOverlay; 