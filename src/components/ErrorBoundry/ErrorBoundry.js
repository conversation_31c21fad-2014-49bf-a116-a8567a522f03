import React, { Component } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Platform } from 'react-native';
import rollbar from '../../utils/rollbar/rollbar';
import { colors } from '../../theme/theme';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import AsyncStorage from '@react-native-async-storage/async-storage';

class ErrorBoundary extends Component {
    constructor(props) {
        super(props);
        this.state = { 
            hasError: false,
            error: null,
            errorInfo: null
        };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        console.error('ErrorBoundary caught an error:', error, errorInfo);
        
        this.setState({
            error,
            errorInfo
        });

        // Log to Rollbar
        rollbar.error(error, errorInfo);

        // Log to console for debugging
        if (__DEV__) {
            console.error('ErrorBoundary Error:', error);
            console.error('ErrorBoundary ErrorInfo:', errorInfo);
        }
    }

    handleReload = async () => {
        try {
            // Clear critical cache
            const keysToClear = [
                'navigation_state',
                'apollo_cache',
                'stream_chat_state',
                'temp_data'
            ];

            for (const key of keysToClear) {
                await AsyncStorage.removeItem(key);
            }

            // Reset error state
            this.setState({ 
                hasError: false, 
                error: null, 
                errorInfo: null 
            });

            // Force app reload by clearing global clients
            if (global.apolloClient) {
                try {
                    await global.apolloClient.clearStore();
                } catch (e) {
                    console.warn('Could not clear Apollo cache:', e);
                }
            }

            if (global.streamClient) {
                try {
                    await global.streamClient.disconnectUser();
                } catch (e) {
                    console.warn('Could not disconnect Stream client:', e);
                }
            }

        } catch (error) {
            console.error('Error during reload:', error);
            Alert.alert(
                'Reload Error',
                'There was an error reloading the app. Please restart the app manually.',
                [{ text: 'OK' }]
            );
        }
    }

    handleReportError = () => {
        const { error, errorInfo } = this.state;
        
        // Create error report
        const errorReport = {
            error: error?.toString(),
            stack: error?.stack,
            errorInfo: errorInfo,
            timestamp: new Date().toISOString(),
            platform: Platform.OS,
            version: require('react-native-device-info').getVersion()
        };

        console.log('Error Report:', errorReport);
        
        Alert.alert(
            'Error Reported',
            'Thank you for reporting this error. Our team will investigate.',
            [{ text: 'OK' }]
        );
    }

    render() {
        if (this.state.hasError) {
            return (
                <View style={styles.container}>
                    <View style={styles.content}>
                        <Text style={styles.headline}>Oops! Something went wrong</Text>
                        <Text style={styles.description}>
                            We're having trouble loading this page. This might be due to the app being in the background for too long.
                        </Text>
                        
                        <View style={styles.buttonContainer}>
                            <TouchableOpacity 
                                style={styles.reloadButton}
                                onPress={this.handleReload}
                                activeOpacity={0.8}
                            >
                                <Text style={styles.reloadButtonText}>Reload App</Text>
                            </TouchableOpacity>
                            
                            <TouchableOpacity 
                                style={styles.reportButton}
                                onPress={this.handleReportError}
                                activeOpacity={0.8}
                            >
                                <Text style={styles.reportButtonText}>Report Error</Text>
                            </TouchableOpacity>
                        </View>

                        {__DEV__ && this.state.error && (
                            <View style={styles.debugContainer}>
                                <Text style={styles.debugTitle}>Debug Information:</Text>
                                <Text style={styles.debugText}>
                                    {this.state.error.toString()}
                                </Text>
                            </View>
                        )}
                    </View>
                </View>
            );
        }

        return this.props.children;
    }
}

export default ErrorBoundary;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
        justifyContent: 'center',
        alignItems: 'center',
        padding: Spacing.SCALE_16
    },
    content: {
        alignItems: 'center',
        maxWidth: 300,
    },
    headline: {
        padding: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_10,
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_21,
        fontWeight: '500',
        color: colors.fadeBlack,
        lineHeight: Typography.FONT_SIZE_14,
        textAlign: 'center',
    },
    description: {
        textAlign: 'center',
        marginBottom: Spacing.SCALE_20,
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        color: colors.fadeBlack,
        lineHeight: 22,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
        marginTop: Spacing.SCALE_20,
    },
    reloadButton: {
        backgroundColor: colors.primary,
        paddingHorizontal: 20,
        paddingVertical: 12,
        borderRadius: 8,
        minWidth: 120,
        marginHorizontal: 8,
    },
    reloadButtonText: {
        color: colors.white,
        fontSize: 16,
        fontWeight: '600',
        textAlign: 'center',
    },
    reportButton: {
        backgroundColor: colors.lightGray,
        paddingHorizontal: 20,
        paddingVertical: 12,
        borderRadius: 8,
        minWidth: 120,
        marginHorizontal: 8,
    },
    reportButtonText: {
        color: colors.text,
        fontSize: 16,
        fontWeight: '600',
        textAlign: 'center',
    },
    debugContainer: {
        marginTop: Spacing.SCALE_20,
        padding: Spacing.SCALE_10,
        backgroundColor: colors.lightGray,
        borderRadius: 8,
        width: '100%',
    },
    debugTitle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: colors.text,
        marginBottom: 8,
    },
    debugText: {
        fontSize: 12,
        color: colors.textSecondary,
        fontFamily: 'monospace',
    },
});
