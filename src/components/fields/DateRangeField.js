import React, { useContext, useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Image, Platform } from 'react-native';
import { FormContext } from '../../forms/FormContext';
import { colors } from '../../theme/theme';
import Moment from 'moment';
import moment from 'moment';

import CalendarIcon from '../../assets/images/calendar.svg';
import useQuery from '../../hooks/useQuery';
import { FETCH_INTERNATIONAL_CLUB_DURATION, FETCH_LOACAL_CLUB_DURATION } from '../../graphql/queries/clubDuration';
import { AuthContext } from '../../context/AuthContext';
import { handleTimeFormat } from '../timeFormatComponent/handleTimeFormat';
import DatePickerSvgIcon from '../../assets/svg/DatePickerSvgIcon.svg'

export default function DateRangeField({
    setModal,
    style,
    closedDates,
    disabledDates = [],
    placeholder = 'Date Range',
    fontFamily = 'Ubuntu-Light',
    singular,
    clubsCountryCode,
    club,
    placeholderFontSize = 14,
    placeholderFontWeight = '500',
    borderBottomColor = 'rgba(0,0,0,0.3)',
    textColor = 'black',
    dateDisplayStyle={}
}) {
    const { form, updateFields, errors } = useContext(FormContext);
    const { user } = useContext(AuthContext);
    const [dateDisplay, setDateDisplay] = useState(placeholder);
    let localClub = useQuery(FETCH_LOACAL_CLUB_DURATION)

    let internationalClub = useQuery(FETCH_INTERNATIONAL_CLUB_DURATION)


    useEffect(() => {
        if (form?.start_date) {
            try {
                const startDate = Moment(form.start_date);
                let dateString = handleTimeFormat(startDate);
                let offerStartDate = moment(
                    moment(form?.start_date).format('MM/DD/YYYY'),
                );
                if (form.end_date) {
                    const endDate = Moment(form.end_date).format('MM/DD/YYYY');
                    if (!moment(form.end_date).isSame(moment(form.start_date), 'day')) {
                        dateString = `${handleTimeFormat(
                            startDate,
                        )} - ${handleTimeFormat(Moment(form?.end_date))}`;
                    }
                }
                setDateDisplay(dateString);
            } catch (error) {
                console.warn('Error formatting date in DateRangeField:', error);
                setDateDisplay(placeholder);
            }
        } else {
            setDateDisplay(placeholder);
        }
    }, [form]);

    const notes = disabledDates?.length > 0 ? 'Some of the offer dates fall on dates when the club is closed, please amend the offer dates or the club closure dates' : ''

    return (
        <>
            <TouchableOpacity
                onPress={() =>
                    setModal({
                        start_date: form.start_date,
                        end_date: form.end_date,
                        updateFields,
                        disabledDates,
                        closedDates,
                        notes
                    })
                }
                style={{
                    width: '100%',
                    paddingVertical: 10,
                    borderBottomWidth: 1,
                    borderBottomColor:
                        errors.start_date || errors.end_date
                            ? 'red'
                            : borderBottomColor,
                }}>
                <View
                    style={{
                        flexDirection: 'row',
                        width: '100%',
                        justifyContent: 'space-between',
                    }}>
                    <Text
                        style={{
                            fontFamily,
                            color: form.start_date ? textColor : colors.darkgray,
                            fontWeight: placeholderFontWeight,
                            fontSize: placeholderFontSize,
                            color: form.start_date ? colors.dark_charcoal : colors.darkgray,
                            ...dateDisplayStyle
                        }}>
                        {dateDisplay}
                    </Text>
                </View>
                <View style={{ position: 'absolute', right: 5, top: 10 }}>
                        <DatePickerSvgIcon width={15} height={15}/>

                </View>
            </TouchableOpacity>
            {(errors.start_date || errors.end_date) && (
                <Text
                    style={{
                        color: 'red',
                        fontFamily: 'Ubuntu-Light',
                        textAlign: 'center',
                        fontSize: 12,
                        marginTop: 8,
                    }}>
                    {singular ? 'Date is required' : 'Date Range is required'}
                </Text>
            )}
        </>
    );
}
