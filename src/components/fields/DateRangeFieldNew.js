import React, { useContext, useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import Moment from 'moment';

import { FormContext } from '../../forms/FormContext';
import { colors } from '../../theme/theme';
import CalendarIcon from '../../assets/svg/DatePickerSvgIcon.svg'
import { AuthContext } from '../../context/AuthContext';
import useQuery from '../../hooks/useQuery';
import {
    FETCH_INTERNATIONAL_CLUB_DURATION,
    FETCH_LOACAL_CLUB_DURATION,
} from '../../graphql/queries/clubDuration';
import { SEARCH_CLUBS } from '../../service/EndPoint';
import { fetcher } from '../../service/fetcher';
import moment from 'moment';
import { handleTimeFormat } from '../timeFormatComponent/handleTimeFormat';

export default function DateRangeFieldNew({
    setModal,
    disabledDates = [],
    placeholder = 'Date Range',
    singular,
    selectedClubNew = {}
}) {
    const { form, updateFields, errors, updateForm } = useContext(FormContext);
    const { user } = useContext(AuthContext);
    const [dateDisplay, setDateDisplay] = useState();
    const [clubs, setClubs] = useState();

    let localClub = useQuery(FETCH_LOACAL_CLUB_DURATION);

    let internationalClub = useQuery(FETCH_INTERNATIONAL_CLUB_DURATION);

    useEffect(() => {
        if (selectedClubNew && Object.keys(selectedClubNew).length)
            updateForm('club', selectedClubNew)
    }, [selectedClubNew])

    useEffect(() => {
        if (form?.club?.name || form?.club) {
            getClubsName();
        }
        console.log("form?.start_date", form?.start_date)
        console.log("form.end_date", form.end_date)
        if (form?.start_date) {
            try {
                const startDate = Moment.utc(form.start_date);
                let dateString = startDate?.format('MM/DD/YYYY');
                setDateDisplay(handleTimeFormat(startDate));
                if (form.end_date) {
                    const endDate = Moment.utc(form.end_date)
                    if (!moment(form.start_date).isSame(moment(form.end_date), 'day')) {
                        dateString = `${handleTimeFormat(
                            startDate,
                        )} - ${handleTimeFormat(endDate)}`;
                        setDateDisplay(dateString);
                    }
                }
            } catch (error) {
                console.warn('Error formatting date in DateRangeFieldNew:', error);
                setDateDisplay(placeholder);
            }
        }
    }, [form]);

    function getClubsName() {
        const body = {
            searchValue: form?.club?.name || form?.club[0],
            userId: user?.id,
        };

        if (body?.searchValue) {
            fetcher({
                endpoint: SEARCH_CLUBS,
                method: 'POST',
                body,
            }).then((res) => {
                setClubs(res?.clubs[0]?.country_code);
            });
        }
    }
    return (
        <>
            <TouchableOpacity
                onPress={() =>
                    form?.club
                        ? setModal({
                            start_date:
                                moment
                                    .utc(form?.start_date)
                                    .format('YYYY-MM-DD') ||
                                moment
                                    .utc(moment(form?.start_date))
                                    .format('YYYY-MM-DD'),
                            end_date:
                                moment
                                    .utc(form?.end_date)
                                    .format('YYYY-MM-DD') ||
                                moment
                                    .utc(moment(form?.end_date))
                                    .format('YYYY-MM-DD'),
                            maxDate: Moment()
                                .add(
                                    (form?.club?.country_code
                                        ? form?.club?.country_code
                                        : clubs
                                    )?.toLowerCase() ===
                                        user?.phone_number_details?.countryCode?.toLowerCase()
                                        ? localClub?.data?.system_setting[0]
                                            ?.value?.value
                                        : internationalClub?.data
                                            ?.system_setting[0]?.value
                                            ?.value,
                                    'months',
                                )
                                .format(),
                            updateFields,
                            disabledDates,
                        })
                        : Alert.alert('', 'Please select the Club first!', [
                            {
                                text: 'OK',
                                onPress: () =>{},
                            },
                        ])
                }
                style={{
                    flexDirection: 'row',
                    paddingVertical: 10,
                    borderBottomWidth: 0.5,
                    borderBottomColor:
                        errors.start_date || errors.end_date
                            ? 'red'
                            : '#C4C4C4',
                }}>
                <View
                    style={{
                        flex: 1,
                    }}>
                    <Text
                        style={{
                            fontFamily: 'Ubuntu-Light',
                            color: dateDisplay ? colors.black : colors.darkgray,
                        }}>
                        {dateDisplay ? dateDisplay : placeholder}
                    </Text>
                </View>
                <CalendarIcon height={18} width={18} />
            </TouchableOpacity>
            {(errors.start_date || errors.end_date) && (
                <Text
                    style={{
                        color: 'red',
                        fontFamily: 'Ubuntu-Light',
                        textAlign: 'center',
                        fontSize: 12,
                        marginTop: 8,
                    }}>
                    {singular ? 'Date is required' : 'Date Range is required'}
                </Text>
            )}
        </>
    );
}
