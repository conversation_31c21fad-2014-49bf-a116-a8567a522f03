import React, { useContext, useEffect, useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Moment from 'moment';

import { colors } from '../../theme/theme';
import { AuthContext } from '../../context/AuthContext';
import { Edit } from '../../assets/images/svg';
import useQuery from '../../hooks/useQuery';
import GET_CLUB_DATA from '../../graphql/queries/getClubData';
import useClient from '../../hooks/useClient';
import { FETCH_INTERNATIONAL_CLUB_DURATION, FETCH_LOACAL_CLUB_DURATION } from '../../graphql/queries/clubDuration';
import { calculateGuestTimeRestrictionDates } from '../../utils/helpers/CreateClubHelper';

export default function DateRangeFieldNew1({
    setModal,
    placeholder = 'Date Range',
    singular,
    start_date,
    end_date,
    club_id,
    game_id,
    request_id,
}) {
    const { user } = useContext(AuthContext);
    const [dateDisplay, setDateDisplay] = useState();
    const client = useClient();
    const [club, setClub] = useState()
    const [disabledDates, setDisabledDates] = useState([]);
    let localClub = useQuery(FETCH_LOACAL_CLUB_DURATION);

    let internationalClub = useQuery(FETCH_INTERNATIONAL_CLUB_DURATION);

    let month =
        club?.country_code == user?.phone_number_details?.countryCode
            ? localClub?.data?.system_setting[0]?.value?.value
            : internationalClub?.data?.system_setting[0]?.value?.value;
    const { data, aggregate } = useQuery(GET_CLUB_DATA, {
        club_id: club_id
    })

    useEffect(() => {
        const getClubDetails = async () => {
            const { view_club_search } = await client.request(`{
            view_club_search(where: {id: {_eq: ${club_id}}}) {
                id
                name
                user_array
                guest_time_restrictions
                closure_period
                guest_fee,
                country_code
            }
        }`);
            const club = view_club_search?.length > 0 ? view_club_search[0] : null;
            setClub(club);
            if (club?.closure_period) {
                const dates = calculateGuestTimeRestrictionDates(
                    club?.closure_period,
                    club?.guest_time_restrictions,
                    (month * 12),
                );
                setDisabledDates(dates);
            }
        }
        getClubDetails()
    }, [month])

    useEffect(() => {
        if (start_date) {
            try {
                const startDate = Moment(start_date);
                let dateString = startDate?.format('MM/DD/YYYY');
                if (end_date) {
                    const endDate = Moment(end_date).format('MM/DD/YYYY')
                    if (!startDate.isSame(endDate, 'day')) {
                        dateString = `${dateString} - ${endDate}`;
                    }
                }
                setDateDisplay(dateString);
            } catch (error) {
                console.warn('Error formatting date in DateRangeFieldNew1:', error);
                setDateDisplay(placeholder);
            }
        }
    }, [start_date]);


    return (
        <>
            <TouchableOpacity
                onPress={() => {
                    setModal({
                        maxDate: Moment().add((club?.country_code)?.toLowerCase() === (user?.phone_number_details?.countryCode)?.toLowerCase()
                            ? (localClub?.data?.system_setting[0]?.value?.value * 12)
                            : (internationalClub?.data?.system_setting[0]?.value?.value * 12)
                            , 'months').format('YYYY-MM-DD'),
                        disabledDates:disabledDates,
                        game_id: game_id,
                        request_id: request_id
                    })
                }

                }
                style={{
                    flexDirection: 'row',
                    marginBottom: 0,
                    paddingBottom: 0
                }}>
                <View
                    style={{
                        flex: 1,
                        marginBottom: 0,
                        paddingBottom: 0

                    }}>
                    <Text
                        style={{
                            fontFamily: 'Ubuntu-Light',
                            color: dateDisplay ? colors.black : colors.darkgray,
                            marginBottom: 0,
                            paddingBottom: 0
                        }}>
                        {dateDisplay ? dateDisplay : placeholder}
                    </Text>
                </View>
                <Edit height={20} width={20} />
            </TouchableOpacity>
        </>
    );
}
