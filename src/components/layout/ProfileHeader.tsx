import React, { useContext } from 'react';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView, View, TouchableOpacity, StyleSheet, Platform, ViewStyle, TextStyle } from 'react-native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import EditProfileTeal from '../../assets/images/EditProfileTeal.svg';
import { Back } from '../../assets/images/svg';
import ClubMemberIcon from '../../assets/images/club-member.svg';
import TGText from '../fields/TGText';
import { GlobalContext } from '../../context/contextApi';
import {
    CreateOfferIcon,
    DeleteGreyIcon,
    MapIconBlack,
    MapIconWhite,
    MapListIconBlack,
    MapListIconWhite,
    NewFilterIcon,
    NewPlusSquare,
    TripleDot,
} from '../../assets/svg';
import { colors } from '../../theme/theme';
import { RootStackParamList } from '../../interface/type';
import { StreamChatContext } from '../../context/StreamChatContext';

interface ProfileHeaderProps {
    title?: string;
    editing?: boolean;
    iconShow?: string;
    onClick?: () => void;
    showEditIcon?: boolean;
    onClickEditIcon?: () => void;
    headerTitleStyle?: TextStyle;
    backButtonFillColor?: string;
    backButtonStyle?: ViewStyle;
    rightIconStyle?: ViewStyle;
    containerStyle?: ViewStyle;
    filterTealDot?: boolean;
    showToggleIcon?: boolean;
    activeView?: string;
    setActiveView?: (view: string) => void;
    showFilter?: boolean;
    showOfferIcon?: boolean;
    handleOfferIconClick?: () => void;
    showTripleDot?: boolean;
    onPressTripleDot?: () => void;
    isComeFromChat?: boolean;
}

export default function ProfileHeader({
    title = '',
    editing = false,
    iconShow = '',
    onClick = () => {},
    showEditIcon = false,
    onClickEditIcon = () => {},
    headerTitleStyle = {},
    backButtonFillColor = 'white',
    backButtonStyle = {},
    rightIconStyle = {},
    containerStyle = {},
    filterTealDot = false,
    showToggleIcon = false,
    activeView = 'MAP',
    setActiveView = () => {},
    showFilter = false,
    showOfferIcon = false,
    handleOfferIconClick = () => {},
    showTripleDot = false,
    onPressTripleDot = () => {},
    isComeFromChat = false,
}: ProfileHeaderProps) {
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const actions = useContext(GlobalContext)?.actions;
    const { setChannel, previousChannel } = useContext(StreamChatContext);
    return (
        <SafeAreaView style={{ width: '100%', flexDirection: 'row' }}>
            <View
                style={{
                    alignItems: 'center',
                    flexDirection: 'row',
                    paddingHorizontal: Spacing.SCALE_16,
                    paddingTop: Platform.OS === 'android' ? Spacing.SCALE_25 : Spacing.SCALE_10,
                    flex: 1,
                    paddingBottom: Spacing.SCALE_18,
                    ...containerStyle,
                }}>
                <TouchableOpacity
                    onPress={() => {
                        if (editing) onClick();
                        else {
                            if (navigation?.canGoBack()) {
                                if (isComeFromChat) {
                                    setChannel(previousChannel);
                                    actions?.updatedChannelAction(previousChannel);
                                    setTimeout(() => {
                                        navigation.goBack();
                                    }, 1000);
                                } else {
                                    navigation.goBack();
                                }
                            } else {
                                navigation.reset({
                                    index: 0,
                                    routes: [{ name: 'BottomTabNavigation' }],
                                });
                            }
                        }
                    }}
                    style={{
                        width: Size.SIZE_26,
                        height: Size.SIZE_26,
                        alignItems: 'flex-start',
                        justifyContent: 'center',
                        ...backButtonStyle,
                    }}>
                    <Back fill={backButtonFillColor} />
                </TouchableOpacity>
                <View
                    style={{
                        flex: 1,
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                    <TGText
                        style={{
                            flex: 1,
                            color: colors.whiteRGB,
                            fontSize: Typography.FONT_SIZE_16,
                            textAlign: 'center',
                            fontFamily: 'Ubuntu-Medium',
                            fontWeight: '500',
                            ...headerTitleStyle,
                        }}>
                        {title}
                    </TGText>
                    {iconShow === 'Club Member' && (
                        <TouchableOpacity onPress={() => onClick()} style={styles.memberIconWrapper}>
                            <ClubMemberIcon height={20} width={20} />
                        </TouchableOpacity>
                    )}

                    {(iconShow === 'Edit Profile' || showEditIcon) && !editing && (
                        <TouchableOpacity
                            style={[styles.editIconWrapper, rightIconStyle]}
                            onPress={() => {
                                if (showEditIcon) {
                                    onClickEditIcon();
                                } else onClick();
                            }}>
                            <EditProfileTeal height={20} width={20} />
                        </TouchableOpacity>
                    )}
                    {showToggleIcon && title === 'Offers' && (
                        <TouchableOpacity
                            style={styles.mapToggle}
                            onPress={() => {
                                if (setActiveView && activeView) {
                                    setActiveView(activeView === 'MAP' ? 'LIST' : 'MAP');
                                }
                            }}>
                            {activeView === 'MAP' ? (
                                <View style={styles.mapToggleIconContainer}>
                                    <View style={styles.mapToggleIconTeal}>
                                        <MapIconWhite />
                                    </View>
                                    <View style={styles.mapToggleIcon}>
                                        <MapListIconBlack />
                                    </View>
                                </View>
                            ) : (
                                <View style={styles.mapToggleIconContainer}>
                                    <View style={styles.mapToggleIcon}>
                                        <MapIconBlack />
                                    </View>
                                    <View style={styles.mapToggleIconTeal}>
                                        <MapListIconWhite />
                                    </View>
                                </View>
                            )}
                        </TouchableOpacity>
                    )}
                    {showOfferIcon && (
                        <TouchableOpacity
                            style={[styles.filterIcon, { marginRight: Spacing.SCALE_12 }]}
                            onPress={handleOfferIconClick}>
                            <CreateOfferIcon />
                        </TouchableOpacity>
                    )}
                    {(iconShow === 'History' || showFilter) && (
                        <TouchableOpacity style={title === 'Offers' ? styles.filterIcon : {}} onPress={() => onClick()}>
                            <NewFilterIcon />
                            {filterTealDot && <View style={styles.filterTealDot} />}
                        </TouchableOpacity>
                    )}
                    {(iconShow === 'requested-history' || iconShow === 'received-history') && (
                        <TouchableOpacity onPress={() => onClick()}>
                            <DeleteGreyIcon />
                        </TouchableOpacity>
                    )}
                    {iconShow === title && (
                        <TouchableOpacity onPress={onClick} style={{ marginLeft: Spacing.SCALE_12 }}>
                            <NewPlusSquare height={20} width={20} />
                        </TouchableOpacity>
                    )}
                    {showTripleDot && (
                        <TouchableOpacity
                            onPress={onPressTripleDot}
                            style={{
                                marginRight: Spacing.SCALE_5,
                                width: Size.SIZE_20,
                                height: Size.SIZE_20,
                                alignItems: 'flex-end',
                                justifyContent: 'center',
                            }}>
                            <TripleDot height={Size.SIZE_20} width={Size.SIZE_5} />
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    editIconWrapper: {
        justifyContent: 'center',
    },
    memberIconWrapper: {
        marginRight: Spacing.SCALE_10,
        justifyContent: 'center',
    },
    filterTealDot: {
        width: 8,
        height: 8,
        borderRadius: 50,
        backgroundColor: colors.tealRgb,
        position: 'absolute',
        right: -2,
        borderWidth: 1,
        borderColor: colors.whiteRGB,
        top: -2,
    },
    mapToggle: {
        borderRadius: Size.SIZE_8,
        padding: Spacing.SCALE_4,
        backgroundColor: colors.lightgray,
        marginRight: Spacing.SCALE_12,
    },
    mapToggleIconTeal: {
        height: Size.SIZE_20,
        width: Size.SIZE_20,
        backgroundColor: colors.tealRgb,
        borderRadius: Size.SIZE_6,
        alignItems: 'center',
        justifyContent: 'center',
    },
    mapToggleIcon: {
        height: Size.SIZE_20,
        width: Size.SIZE_20,
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_6,
        alignItems: 'center',
        justifyContent: 'center',
    },
    mapToggleIconContainer: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_4,
    },
    filterIcon: {
        borderWidth: 1,
        borderColor: colors.darkgray,
        borderRadius: Size.SIZE_8,
        padding: Spacing.SCALE_4,
    },
    tealDot: {
        width: Size.SIZE_6,
        height: Size.SIZE_6,
        backgroundColor: colors.tealRgb,
        borderRadius: Size.SIZE_50,
        position: 'absolute',
        right: -2,
        top: -2,
        borderWidth: 1,
        borderColor: colors.whiteRGB,
    },
});
