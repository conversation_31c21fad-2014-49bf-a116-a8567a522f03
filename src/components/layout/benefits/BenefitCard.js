import React, { useState } from 'react';
import { View, Text, Dimensions, TouchableOpacity, Linking, StyleSheet } from 'react-native';
import FastImage from 'react-native-fast-image';

import { colors } from '../../../theme/theme';
import CalendarIcon from '../../../assets/images/calendar.svg';
import DisclaimerPopUp from '../events/DisclaimerPopUp';
import RevalPromoCode from './RevalPromoCode';
import TGText from '../../fields/TGText';
import { updateBenefitCount } from './updateBenifit';
import { BENEFIT_INFO_COUNT_TYPE, REVEAL_BENEFIT_COUNT_TYPE } from '../../../screens/my-TG-Stream-Chat/constants';
import { handleTimeFormat } from '../../timeFormatComponent/handleTimeFormat';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import useThumbnail from '../../../hooks/useThumbnail';
import constants from '../../../utils/constants/constants';

const { width, height } = Dimensions.get('window');

export default function BenefitCard({ benefit, onPress, user }) {
    const [showPopup, setShowPopup] = useState(false);
    const [requestShowPopup, setRequestShowPopup] = useState(false);
    const benefit_photo = useThumbnail(benefit.photo, constants.ImageSize[1280])?.thumbnailUrl;

    return (
        <>
            <TouchableOpacity style={styles.benefitCardWrapper} onPress={onPress}>
                <FastImage
                    source={{
                        uri: benefit_photo,
                        priority: FastImage.priority.high,
                    }}
                    resizeMode={FastImage.resizeMode.contain}
                    style={styles.imageStyle}
                />
                <View style={styles.cardBodyWrapper}>
                    <View style={styles.box}>
                        <View
                            style={{
                                maxWidth: benefit.dateRange ? width - 50 : null,
                            }}>
                            <Text style={styles.benefitTitleWrapper}>{benefit.title}</Text>
                            <Text style={styles.categoryNameStyle}>{benefit?.benefit_category?.name}</Text>
                            {benefit.dateRange && (
                                <View style={styles.dateWrapperStyle}>
                                    <CalendarIcon height={20} width={20} />
                                    <Text style={styles.calenderTextStyle}>
                                        {handleTimeFormat(benefit.dateRange.from, true) ===
                                        handleTimeFormat(benefit.dateRange.to, true)
                                            ? handleTimeFormat(benefit.dateRange.from, true)
                                            : `${handleTimeFormat(benefit.dateRange.from, true)} - ${handleTimeFormat(
                                                  benefit.dateRange.to,
                                                  true,
                                              )}`}
                                    </Text>
                                </View>
                            )}
                        </View>
                    </View>
                    <Text numberOfLines={3} style={styles.benefitDescriptionWrapper}>
                        {benefit.description}
                    </Text>
                    <View style={styles.revealDiscountBtnWrapper}>
                        <TouchableOpacity
                            onPress={() => {
                                updateBenefitCount({
                                    userId: user.id,
                                    type: REVEAL_BENEFIT_COUNT_TYPE,
                                    benefitId: benefit?.id,
                                });
                                setShowPopup(true);
                            }}>
                            <Text style={styles.revealBenefitTextStyle}>REVEAL BENEFIT</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </TouchableOpacity>
            <RevalPromoCode
                visible={showPopup}
                promoMesage={benefit.promo_code}
                onPressClose={() => setShowPopup(false)}
                onPress={() => {
                    setRequestShowPopup(true);
                    setShowPopup(false);
                }}
            />

            <DisclaimerPopUp
                visible={requestShowPopup}
                onPressClose={() => setRequestShowPopup(false)}
                onPress={() => {
                    if (benefit?.sendToEmail && benefit?.email) {
                        Linking.openURL(`mailto:${benefit?.email}`);
                    }

                    if (benefit?.sendToWebsite && benefit?.websiteURL) {
                        const linkURL =
                            benefit?.websiteURL.includes('http://') || benefit?.websiteURL.includes('https://')
                                ? benefit?.websiteURL
                                : `https://${benefit?.websiteURL}`;
                        Linking.openURL(linkURL);
                    }
                    updateBenefitCount({
                        userId: user.id,
                        type: BENEFIT_INFO_COUNT_TYPE,
                        benefitId: benefit?.id,
                    });
                    setRequestShowPopup(false);
                }}
            />
        </>
    );
}

const styles = StyleSheet.create({
    dateWrapperStyle: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: 5,
        width: width - 100,
    },
    benefitCardWrapper: {
        marginBottom: Spacing.SCALE_10,
    },
    imageStyle: {
        width: width - 20,
        height: height / 4,
        borderTopLeftRadius: Size.SIZE_10,
        borderTopRightRadius: Size.SIZE_10,
        backgroundColor: colors.white,
    },
    cardBodyWrapper: {
        padding: Spacing.SCALE_15,
        backgroundColor: colors.white,
        paddingTop: Spacing.SCALE_25,
        borderBottomLeftRadius: Size.SIZE_10,
        borderBottomRightRadius: Size.SIZE_10,
    },
    box: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    benefitTitleWrapper: {
        fontFamily: 'Ubuntu-Bold',
        fontSize: Typography.FONT_SIZE_16,
    },
    categoryNameStyle: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_15,
        color: colors.darkgray,
        paddingTop: Spacing.SCALE_5,
        textTransform: 'uppercase',
    },
    calenderTextStyle: {
        marginLeft: Spacing.SCALE_10,
        fontFamily: 'Ubuntu-Regular',
        color: colors.darkgray,
    },
    benefitDescriptionWrapper: {
        paddingTop: Spacing.SCALE_10,
        paddingBottom: Spacing.SCALE_15,
        fontFamily: 'Ubuntu-Regular',
        color: colors.darkgray,
    },
    revealDiscountBtnWrapper: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
    },
    revealBenefitTextStyle: {
        color: colors.darkteal,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
    },
});
