import React, { useState, useEffect } from 'react';
import {
    View,
    TouchableOpacity,
    Text,
    TouchableWithoutFeedback,
} from 'react-native';
import { Calendar } from 'react-native-calendars';
import Moment from 'moment';
import { colors } from '../../theme/theme';

export default function ClosurePeriodsModal({ modal, setModal }) {
    const [startDate, setStartDate] = useState();
    const [endDate, setEndDate] = useState();

    function getDates() {
        if (startDate && startDate.isValid()) {
            if (endDate && endDate.isValid()) {
                const rangeLength = Math.abs(startDate.diff(endDate, 'day'));
                
                try {
                    const markedDates = new Array(rangeLength + 1)
                        .fill('')
                        .map((_, index) => {
                            const date = startDate
                                .clone()
                                .add(index, 'days')
                                .format('YYYY-MM-DD');
                            return [
                                date,
                                {
                                    startingDay: index === 0,
                                    endingDay: index === rangeLength,
                                    color: colors.darkteal,
                                    textColor: 'white',
                                },
                            ];
                        });
                    return Object.fromEntries(markedDates);
                } catch (error) {
                    console.warn('Error creating date range array:', error);
                    return {
                        [startDate.format('YYYY-MM-DD')]: {
                            startingDay: true,
                            endingDay: true,
                            color: colors.darkteal,
                            textColor: 'white',
                        },
                    };
                }
            } else {
                return {
                    [startDate.format('YYYY-MM-DD')]: {
                        startingDay: true,
                        endingDay: true,
                        color: colors.darkteal,
                        textColor: 'white',
                    },
                };
            }
        } else {
            return {};
        }
    }

    useEffect(() => {
        if (modal.editingPeriodIndex || modal.editingPeriodIndex === 0) {
            const { from, to } = modal.form.closurePeriods[
                modal.editingPeriodIndex
            ];
            setStartDate(Moment(from));
            setEndDate(Moment(to));
        }
    }, []);

    function setDate(day) {
        const date = Moment(day.dateString);
        if (!startDate || date.isBefore(startDate)) {
            setStartDate(date);
        } else {
            setEndDate(date);
        }
    }

    return (
        <View
            style={{
                position: 'absolute',
                left: 0,
                right: 0,
                top: 0,
                bottom: 0,
                paddingHorizontal: 30,
                justifyContent: 'center',
                alignItems: 'center',
            }}>
            <TouchableWithoutFeedback onPress={() => setModal()}>
                <View
                    style={{
                        position: 'absolute',
                        left: 0,
                        right: 0,
                        top: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0,0,0,0.5)',
                    }}
                />
            </TouchableWithoutFeedback>
            <View
                style={{
                    backgroundColor: 'white',
                    borderRadius: 10,
                    width: '100%',
                    overflow: 'hidden',
                }}>
                <Text
                    style={{
                        fontFamily: 'RobotoSlab-Regular',
                        padding: 30,
                        paddingBottom: 15,
                        fontSize: 20,
                        textAlign: 'center',
                    }}>
                    {modal.title}
                </Text>
                <Calendar
                    minDate={new Date()}
                    maxDate={Moment().add(1, 'year').format()}
                    onDayPress={setDate}
                    markedDates={getDates()}
                    markingType={'period'}
                />
                <View
                    style={{
                        flexDirection: 'row',
                        padding: 30,
                        justifyContent: 'space-between',
                    }}>
                    <TouchableOpacity
                        onPress={() => setModal()}
                        style={{
                            paddingHorizontal: 30,
                            paddingVertical: 10,
                            borderRadius: 10,
                        }}>
                        <Text
                            style={{
                                fontFamily: 'Ubuntu-Regular',
                                fontSize: 16,
                                color: colors.darkgray,
                            }}>
                            Cancel
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => {
                            if (startDate)
                                if (
                                    modal.editingPeriodIndex ||
                                    modal.editingPeriodIndex === 0
                                ) {
                                    modal.updateForm(
                                        'closurePeriods',
                                        modal.form.closurePeriods.map(
                                            (period, index) => {
                                                if (
                                                    index ===
                                                    modal.editingPeriodIndex
                                                ) {
                                                    return {
                                                        from: startDate.format(
                                                            'MM/DD/YYYY',
                                                        ),
                                                        to: endDate.format(
                                                            'MM/DD/YYYY',
                                                        ),
                                                    };
                                                } else {
                                                    return period;
                                                }
                                            },
                                        ),
                                    );
                                } else {
                                    modal.updateForm('closurePeriods', [
                                        ...modal.form.closurePeriods,
                                        {
                                            from: startDate.format('MM/DD/YYYY'),
                                            to: endDate ? endDate.format('MM/DD/YYYY') : startDate.format('MM/DD/YYYY'),
                                        },
                                    ]);
                                }
                            setModal();
                        }}
                        style={{
                            backgroundColor: colors.darkteal,
                            paddingHorizontal: 30,
                            paddingVertical: 10,
                            borderRadius: 10,
                        }}>
                        <Text
                            style={{
                                fontFamily: 'Ubuntu-Regular',
                                fontSize: 16,
                                color: 'white',
                            }}>
                            Save
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}
