import React, { useState, useEffect } from 'react';
import {
    View,
    TouchableOpacity,
    Text,
    TouchableWithoutFeedback,
} from 'react-native';
import { Calendar, CalendarList } from 'react-native-calendars';
import Moment from 'moment';
import { colors } from '../../theme/theme';
import TGText from '../fields/TGText';

export default function DateRangeModal({ modal, setModal }) {
    const [startDate, setStartDate] = useState(modal.startDate);
    const [endDate, setEndDate] = useState(modal.endDate);
    const [showClosureMsg, setShowClosureMsg] = useState(false);

    let isShow = false;


    useEffect(() => {
        if (modal.start_date) {
            if (typeof modal.start_date === 'string') {
                const sDate = Moment.utc(modal.start_date) >= Moment.utc() ? Moment.utc(modal.start_date) : Moment.utc()
                setStartDate(sDate);
                setEndDate(Moment.utc(modal.end_date));
            } else {
                const sDate = Moment.utc(modal.start_date) >= Moment.utc() ? Moment.utc(modal.start_date) : Moment.utc()
                setStartDate(sDate);
                setEndDate(Moment.utc(modal.end_date));
            }
        }
    }, [modal]);


    function getDates() {
        let disabledDates = {};
        if (modal.disabledDates && modal.disabledDates.length > 0) {
            disabledDates = modal.disabledDates.reduce((dates, date) => {
                return {
                    ...dates,
                    [date]: {
                        disabled: true,
                        disableTouchEvent: true,
                    },
                };
            }, {});
        }
        if (startDate && startDate.isValid()) {
            if (endDate && endDate.isValid()) {
                try {
                    const rangeLength = Math.abs(Moment.utc(Moment.utc(startDate).format('YYYY-MM-DD')).diff(Moment.utc(Moment.utc(endDate).format('YYYY-MM-DD')), 'day'));

                    const markedDates = new Array(rangeLength + 1)
                        .fill('')
                        .map((_, index) => {
                            const sDate = startDate < endDate ? startDate : endDate
                            const date = Moment.utc(sDate.clone()
                                .add(index, 'days'))
                                .format('YYYY-MM-DD');
                            return [
                                date,
                                {
                                    startingDay: index === 0,
                                    endingDay: index === rangeLength,
                                    color: colors.darkteal,
                                    textColor: 'white',
                                },
                            ];
                        });
                    /////////////////////////////////Closure message///////////////////////////////////////////////////////////////
                    let newD = [];
                    for (let value of Object.values(markedDates)) {
                        newD.push(value[0])
                    }
                    let result = modal?.closedDates?.filter(o1 => newD.some(o2 => o1 === o2));
                    if (result?.length > 0) {
                        // setShowClosureMsg(true)
                        isShow = true
                    } else {
                        // setShowClosureMsg(false)
                        isShow = false
                    }
                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////
                    return { ...Object.fromEntries(markedDates), ...disabledDates };
                } catch (error) {
                    console.warn('Error formatting date range in DateRangeModal:', error);
                    return disabledDates;
                }
            } else {
                try {
                    return {
                        [startDate.format('YYYY-MM-DD')]: {
                            startingDay: true,
                            endingDay: true,
                            color: colors.darkteal,
                            textColor: 'white',
                        },
                        ...disabledDates,
                    };
                } catch (error) {
                    console.warn('Error formatting start date in DateRangeModal:', error);
                    return disabledDates;
                }
            }
        } else {
            return disabledDates;
        }

    }


    function setDate(day) {
        const date = Moment.utc(day.dateString);

        if (startDate && endDate) {
            setStartDate(date)
            setEndDate()
        } else if (startDate) {
            setEndDate(date)
        } else {
            setStartDate(date)
        }
    }

    return (
        <View
            style={{
                position: 'absolute',
                left: 0,
                right: 0,
                top: 0,
                bottom: 0,
                paddingHorizontal: 30,
                justifyContent: 'center',
                alignItems: 'center',
                zIndex: 100,
            }}>
            <TouchableWithoutFeedback onPress={() => setModal()}>
                <View
                    style={{
                        position: 'absolute',
                        left: 0,
                        right: 0,
                        top: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0,0,0,0.5)',
                    }}
                />
            </TouchableWithoutFeedback>
            <View
                style={{
                    backgroundColor: 'white',
                    borderRadius: 10,
                    width: '100%',
                    overflow: 'hidden',
                }}>
                <Text
                    style={{
                        fontFamily: 'RobotoSlab-Regular',
                        padding: 30,
                        paddingBottom: 15,
                        fontSize: 20,
                        textAlign: 'center',
                    }}>
                    Date Range
                </Text>
                <Calendar
                    current={modal.minDate && new Date(modal.minDate) > new Date() ? new Date(modal.minDate) : new Date()}
                    minDate={modal.minDate && Moment(modal.minDate) > Moment() ? Moment.utc(modal.minDate).format('YYYY-MM-DD') : new Date()}
                    maxDate={modal.maxDate
                        ? Moment.utc(modal.maxDate).format('YYYY-MM-DD')
                        : Moment().add(1, 'year').format()
                    }
                    onDayPress={setDate}
                    markedDates={getDates()}
                    markingType={'period'}
                    hideExtraDays={true}
                    disableMonthChange={true}
                />

                {modal?.notes?.length > 0 && isShow &&
                    <TGText style={{
                        fontFamily: 'Ubuntu-Light',
                        fontSize: 12,
                        marginTop: 10,
                        marginHorizontal: 20,
                        color: 'red',
                        textAlign: 'center'
                    }}>{modal.notes}</TGText>
                }
                {modal?.monthNote?.length > 0 &&
                    <TGText style={{
                        fontFamily: 'Ubuntu-Light',
                        fontSize: 12,
                        marginTop: 10,
                        marginHorizontal: 20,
                        color: colors.darkteal,
                        textAlign: 'center'
                    }}>{modal.monthNote}</TGText>
                }
                <View
                    style={{
                        flexDirection: 'row',
                        padding: 20,
                        justifyContent: 'space-between',
                    }}>
                    <TouchableOpacity
                        onPress={() => setModal()}
                        style={{
                            paddingHorizontal: 30,
                            paddingVertical: 10,
                            borderRadius: 10,
                        }}>
                        <Text
                            style={{
                                fontFamily: 'Ubuntu-Regular',
                                fontSize: 16,
                                color: colors.darkgray,
                                textAlign: 'center'
                            }}>
                            Cancel
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => {
                            if (startDate) {
                                modal.updateFields({
                                    start_date: endDate && startDate > endDate ? endDate : startDate,
                                    end_date: endDate && startDate < endDate ? endDate : startDate,
                                    registration_start_date: undefined,
                                });
                            }

                            setModal();
                        }}
                        style={{
                            backgroundColor: colors.darkteal,
                            paddingHorizontal: 30,
                            paddingVertical: 10,
                            borderRadius: 10,
                        }}>
                        <Text
                            style={{
                                fontFamily: 'Ubuntu-Regular',
                                fontSize: 16,
                                color: 'white',
                            }}>
                            Save
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}
