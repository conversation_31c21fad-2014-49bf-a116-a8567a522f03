import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, Text, TouchableWithoutFeedback } from 'react-native';
import { Calendar } from 'react-native-calendars';
import Moment from 'moment';
import moment from 'moment';

import { colors } from '../../theme/theme';
import TGText from '../fields/TGText';
import { fetcher } from '../../service/fetcher';
import { EDIT_GAME_AS_HOST } from '../../service/EndPoint';
import showToast from '../toast/CustomToast';
import { Modal } from 'react-native';

export default function DateRangeModal1({
    modal,
    setModal,
    getRequests,
    receivedAcceptedURL,
    user_id,
    setReceivedAccepted,
    getAllRequest = () => {},
}) {
    const [startDate, setStartDate] = useState(modal.startDate);
    const [endDate, setEndDate] = useState(modal.endDate);

    let isShow = false;

    useEffect(() => {
        if (modal.start_date) {
            if (typeof modal.start_date === 'string') {
                const sDate = Moment(modal.start_date) >= Moment() ? Moment(modal.start_date) : Moment();
                setStartDate(sDate);
            } else {
                const sDate = modal.start_date >= new Date() ? modal.start_date : new Date();
                setStartDate(sDate);
            }
        }
    }, [modal]);

    function getDates() {
        let disabledDates = {};
        if (modal.disabledDates && modal.disabledDates.length > 0) {
            disabledDates = modal.disabledDates.reduce((dates, date) => {
                return {
                    ...dates,
                    [date]: {
                        disabled: true,
                        disableTouchEvent: true,
                    },
                };
            }, {});
        }
        if (startDate && startDate.isValid()) {
            if (endDate && endDate.isValid()) {
                const rangeLength = Math.abs(startDate.diff(endDate, 'day'));
                
                try {
                    const markedDates = new Array(rangeLength + 1).fill('').map((_, index) => {
                        const sDate = startDate < endDate ? startDate : endDate;
                        const date = sDate.clone().add(index, 'days').format('YYYY-MM-DD');

                        return [
                            date,
                            {
                                startingDay: index === 0,
                                endingDay: index === rangeLength,
                                color: colors.darkteal,
                                textColor: 'white',
                            },
                        ];
                    });

                    /////////////////////////////////Closure message///////////////////////////////////////////////////////////////
                    let newD = [];
                    for (let value of Object.values(markedDates)) {
                        newD.push(value[0]);
                    }
                    let result = modal?.closedDates?.filter((o1) => newD.some((o2) => o1 === o2));
                    if (result?.length > 0) {
                        isShow = true;
                    } else {
                        isShow = false;
                    }
                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////
                    return { ...Object.fromEntries(markedDates), ...disabledDates };
                } catch (error) {
                    console.warn('Error creating date range array:', error);
                    return {
                        [startDate.format('YYYY-MM-DD')]: {
                            startingDay: true,
                            endingDay: true,
                            color: colors.darkteal,
                            textColor: 'white',
                        },
                        ...disabledDates,
                    };
                }
            } else {
                return {
                    [startDate.format('YYYY-MM-DD')]: {
                        startingDay: true,
                        endingDay: true,
                        color: colors.darkteal,
                        textColor: 'white',
                    },
                    ...disabledDates,
                };
            }
        } else {
            return disabledDates;
        }
    }

    function setDate(day) {
        const date = Moment(day.dateString);
        if (startDate) {
            setStartDate(date);
        } else {
            setStartDate(date);
        }
    }
    const handlePress = async () => {
        if (startDate) {
            const editGameParams = {
                userId: user_id,
                requestId: modal?.request_id,
                gameDate: Moment(startDate).format('YYYY-MM-DD'),
            };
            fetcher({
                endpoint: EDIT_GAME_AS_HOST,
                method: 'POST',
                body: editGameParams,
            })
                .then((res) => {
                    if (res?.status) {
                        let data = {
                            userId: user_id,
                        };
                        getRequests(receivedAcceptedURL, data).then((requests) => {
                            setReceivedAccepted(requests?.data);
                            getAllRequest();
                        });
                        setModal();
                    } else {
                        showToast({});
                    }
                })
                .catch(() => {
                    showToast({});
                });
        }
    };

    return (
        <Modal visible={true} transparent={true}>
            <View
                style={{
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                    paddingHorizontal: 30,
                    justifyContent: 'center',
                    alignItems: 'center',
                    zIndex: 100,
                }}>
                <TouchableWithoutFeedback onPress={() => setModal()}>
                    <View
                        style={{
                            position: 'absolute',
                            left: 0,
                            right: 0,
                            top: 0,
                            bottom: 0,
                            backgroundColor: 'rgba(0,0,0,0.5)',
                        }}
                    />
                </TouchableWithoutFeedback>
                <View
                    style={{
                        backgroundColor: 'white',
                        borderRadius: 10,
                        width: '100%',
                        overflow: 'hidden',
                    }}>
                    <Text
                        style={{
                            fontFamily: 'RobotoSlab-Regular',
                            padding: 30,
                            paddingBottom: 15,
                            fontSize: 20,
                            textAlign: 'center',
                        }}>
                        Select Date
                    </Text>
                    <Calendar
                        current={
                            modal.minDate && new Date(modal.minDate) > new Date() ? new Date(modal.minDate) : new Date()
                        }
                        minDate={
                            modal.minDate && new Date(modal.minDate) > new Date() ? new Date(modal.minDate) : new Date()
                        }
                        maxDate={
                            modal?.maxDate
                                ? moment(modal.maxDate).format('YYYY-MM-DD')
                                : Moment().add(1, 'year').format('YYYY-MM-DD')
                        }
                        onDayPress={setDate}
                        markedDates={getDates()}
                        markingType={'period'}
                        hideExtraDays={true}
                        disableMonthChange={true}
                    />

                    {modal?.notes?.length > 0 && isShow && (
                        <TGText
                            style={{
                                fontFamily: 'Ubuntu-Light',
                                fontSize: 12,
                                marginTop: 10,
                                marginHorizontal: 20,
                                color: 'red',
                                textAlign: 'center',
                            }}>
                            {modal.notes}
                        </TGText>
                    )}
                    {modal?.monthNote?.length > 0 && (
                        <TGText
                            style={{
                                fontFamily: 'Ubuntu-Light',
                                fontSize: 12,
                                marginTop: 10,
                                marginHorizontal: 20,
                                color: colors.darkteal,
                                textAlign: 'center',
                            }}>
                            {modal.monthNote}
                        </TGText>
                    )}
                    <View
                        style={{
                            flexDirection: 'row',
                            padding: 20,
                            justifyContent: 'space-between',
                        }}>
                        <TouchableOpacity
                            onPress={() => setModal()}
                            style={{
                                paddingHorizontal: 30,
                                paddingVertical: 10,
                                borderRadius: 10,
                            }}>
                            <Text
                                style={{
                                    fontFamily: 'Ubuntu-Regular',
                                    fontSize: 16,
                                    color: colors.darkgray,
                                    textAlign: 'center',
                                }}>
                                Cancel
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => {
                                handlePress();
                            }}
                            style={{
                                backgroundColor: colors.darkteal,
                                paddingHorizontal: 30,
                                paddingVertical: 10,
                                borderRadius: 10,
                            }}>
                            <Text
                                style={{
                                    fontFamily: 'Ubuntu-Regular',
                                    fontSize: 16,
                                    color: 'white',
                                }}>
                                Save
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
}
