import { Keyboard, KeyboardAvoidingView, Modal, Pressable, StyleSheet, Text, View } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import PendingGameIcon from '../../assets/svg/PendingGameIcon.svg';
import { colors } from '../../theme/theme';
import { CancelButton } from '../../components/buttons';
import TealButtonNew from '../../components/buttons/TealButtonNew';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { fetcher } from '../../service/fetcher';
import { CHECK_REQUEST_POPUP, UPDATE_POPUP_STATUS } from '../../service/EndPoint';
import { AuthContext } from '../../context/AuthContext';
import { useNavigation } from '@react-navigation/native';
import routes from '../../config/routes';
import { GlobalContext } from '../../context/contextApi';

const PendingGameRequestPopup = () => {
    const { state, actions } = useContext(GlobalContext);

    const { user } = useContext(AuthContext);
    const [showPopup, setPopup] = useState(false);

    const navigation = useNavigation();

    useEffect(() => {
        checkPendingRequestStatus();
    }, [user]);

    const checkPendingRequestStatus = () => {
        fetcher({
            endpoint: CHECK_REQUEST_POPUP,
            method: 'POST',
            body: {
                userId: user?.id,
            },
        }).then((res) => {
            if (res.status) {
                if (res.data) {
                    setPopup(res.data);
                } else {
                    actions.setHomeScreenPopupState(3);
                }
            } else {
                actions.setHomeScreenPopupState(3);
            }
        });
    };

    const updatePopState = () => {
        fetcher({
            endpoint: UPDATE_POPUP_STATUS,
            method: 'POST',
            body: {
                userId: user?.id,
            },
        }).then((res) => {
            if (res.status) {
                setPopup(res.data);
            }
        });
    };

    const onPressHandler = ({ type }) => {
        switch (type) {
            case 'DISMISS':
                {
                    setPopup(false);
                    updatePopState();
                    actions.setHomeScreenPopupState(3);
                }
                break;
            case 'VIEW':
                {
                    setPopup(false);
                    updatePopState();
                    navigation.navigate(routes.BOTTOM_TAB_NAVIGATION, {
                        screen: 'RequestScreen',
                    });
                    actions.setHomeScreenPopupState(3);
                }
                break;
        }
    };

    if (showPopup)
        return (
            <Modal
                animationIn="fadeInUp"
                transparent={true}
                isVisible={showPopup}
                backdropTransitionOutTiming={1}
                style={styles.modal}>
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : null}
                    style={{ flex: 1 }}
                    keyboardShouldPersistTaps="handled"
                    enabled={true}>
                    <View style={styles.modalBodyWrapper}>
                        <Pressable style={styles.modalBackgroundStyle} onPress={Keyboard.dismiss} />
                        <Pressable style={styles.popupWrapper} onPress={Keyboard.dismiss}>
                            <View style={styles.addFriendIconStyle}>
                                <PendingGameIcon />
                            </View>
                            <Text style={styles.text}>Open Game Request(s)</Text>
                            <View style={styles.textWrapper}>
                                <Text style={styles.textStyle}>
                                    You have Open Game Request(s). Please accept or decline
                                </Text>
                            </View>

                            <View style={styles.btnWrapper}>
                                <CancelButton
                                    customStyle={[styles.customStyle, { backgroundColor: colors.lightgray }]}
                                    textStyle={styles.btn1TextStyle}
                                    text="Dismiss"
                                    onPress={() => onPressHandler({ type: 'DISMISS' })}
                                />
                                <TealButtonNew
                                    btnStyle={styles.customStyle}
                                    textStyle={styles.btn2TextStyle}
                                    text="View"
                                    onPress={() => onPressHandler({ type: 'VIEW' })}
                                    loading={false}
                                />
                            </View>
                        </Pressable>
                    </View>
                </KeyboardAvoidingView>
            </Modal>
        );
    else return null;
};

export default PendingGameRequestPopup;

const styles = StyleSheet.create({
    popupWrapper: {
        width: '100%',
        position: 'absolute',
        bottom: 0,
        backgroundColor: 'white',
        borderTopLeftRadius: Size.SIZE_8,
        borderTopRightRadius: Size.SIZE_8,
        alignItems: 'center',
        paddingVertical: Spacing.SCALE_15,
    },
    modal: {
        paddingHorizontal: 0,
        marginHorizontal: 0,
        paddingVertical: 0,
        marginVertical: 0,
    },
    modalBodyWrapper: {
        flex: 1,
    },
    modalBackgroundStyle: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    text: {
        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_24,
        fontWeight: '400',
        lineHeight: Size.SIZE_28,
        marginTop: Spacing.SCALE_15,
        fontFamily: 'Ubuntu-Medium',
        color: '#333333',
    },
    textStyle: {
        textAlign: 'center',
        color: '#666666',
        fontSize: Typography.FONT_SIZE_14,
        marginTop: Spacing.SCALE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_15,
    },
    btnWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.SCALE_20,
        paddingVertical: Spacing.SCALE_15,
        marginTop: Spacing.SCALE_10,
        width: '100%',
    },

    cancelBtnStyle: {
        color: 'rgba(0, 122, 255, 1)',
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '600',
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
    },
    customStyle: {
        borderRadius: Size.SIZE_8,
        height: Size.SIZE_50,
        width: '46%',
    },
    btn1TextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_18,
        fontWeight: '500',
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
    },
    btn2TextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_18,
        fontWeight: '500',
        color: colors.white,
        fontFamily: 'Ubuntu-Medium',
    },
});
