import moment from 'moment';

const handleTimeFormat = (utcTime, isString = false) => {
    // Check if utcTime is valid
    if (!utcTime || utcTime === null || utcTime === undefined) {
        return '';
    }
    
    let date = '';
    try {
        if (isString) {
            date = moment.utc(utcTime, 'MM/DD/YYYY').format('Do MMMM YYYY');
        } else {
            date = moment.utc(utcTime).format('Do MMMM YYYY');
        }
    } catch (error) {
        console.warn('Error formatting date:', error);
        return '';
    }
    return date;
};
export { handleTimeFormat };
