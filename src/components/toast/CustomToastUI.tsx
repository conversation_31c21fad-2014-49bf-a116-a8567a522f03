import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import React, { useEffect } from 'react';
import Animated, { Easing, useAnimatedStyle, useSharedValue, withRepeat, withTiming } from 'react-native-reanimated';
import { ToastProps } from 'react-native-toast-message';

// theme, utils and assets imports
import { Size } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import { CrossIcon } from '../../assets/svg';
import { ERROR, SUCCESS } from '../../utils/constants/strings';

const CustomToastUI = (props: ToastProps) => {
    // @ts-ignore
    const { hide, isVisible, onPress, position, show, text1, text1Style, text2, text2Style, type } = props?.props;

    const rotation = useSharedValue(0);

    useEffect(() => {
        rotation.value = withRepeat(
            withTiming(360, {
                duration: 1000,
                easing: Easing.linear,
            }),
            -1, // Infinite loop
            false, // No reversing
        );
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
        return {
            transform: [{ rotate: `${rotation.value}deg` }],
        };
    });
    
    return (
        <View
            style={[
                styles.container,
                type === ERROR && { backgroundColor: colors.orangeVariant1, borderColor: colors.orangeVariant2 },
            ]}>
            <View style={type === SUCCESS ? styles.iconCircle : styles.iconCircleError}>
                {type === SUCCESS ? <Text style={styles.checkmark}>✓</Text> : <Text style={styles.checkmark}>✕</Text>}
            </View>
            {/* Center: Toast message */}
            <View style={{ width: Size.SIZE_170 }}>
                <Text style={styles.toastText} numberOfLines={3} ellipsizeMode="tail">
                    {text2}
                </Text>
            </View>
            {/* Right: Close (X) icon (placeholder) */}
            <View style={styles.closeCircleWrapper}>
                <Animated.View
                    style={[
                        styles.closeCircle,
                        animatedStyle,
                        type === ERROR && { borderTopColor: colors.orangeVariant1 },
                    ]}
                />
                <TouchableOpacity style={styles.crossIconWrapper} onPress={hide}>
                    <CrossIcon width={Size.SIZE_20} height={Size.SIZE_20} />
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default CustomToastUI;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: Size.SIZE_280,
        backgroundColor: colors.lightTealVariant1, // light green
        borderRadius: Size.SIZE_12,
        borderColor: colors.tealBorderColor,
        paddingHorizontal: Size.SIZE_12,
        paddingVertical: Size.SIZE_10,
        borderWidth: 1,
        position: 'absolute',
        right: Size.SIZE_16, // Or simply 16
        top: Size.SIZE_60, // Optional: adjust based on where you want it vertically
    },
    iconCircle: {
        width: Size.SIZE_24,
        height: Size.SIZE_24,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.brightGreen, // green
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: Size.SIZE_12,
    },
    iconCircleError: {
        width: Size.SIZE_24,
        height: Size.SIZE_24,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.orange, // green
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: Size.SIZE_12,
    },
    checkmark: {
        color: '#fff',
        fontSize: Size.SIZE_14,
        fontWeight: 'bold',
    },
    toastText: {
        fontSize: 14,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    closeCircleWrapper: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
    },
    closeCircle: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_16,
        borderWidth: 2,
        borderColor: '#BDBDBD',
        borderTopColor: colors.lightTealVariant1, // Only top is colored for spinner effect
        position: 'absolute',
        top: 0,
        left: 0,
    },
    crossIconWrapper: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1,
    },
});
