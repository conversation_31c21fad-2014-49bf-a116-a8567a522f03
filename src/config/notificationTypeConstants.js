export default {
    Tga_Friend_Connected: 'tga-friend-connected',
    My_Tg_All_Friend: 'my-tg-all-friend',
    Friend_Request_Accepted: 'friend-request-accepted',
    Friend_Joined_My_Tg_Group: 'friend-joined-my-tg-group',
    Join_Group_Request: 'join-group-request',
    Friend_Request_Declined: 'friend-request-declined',
    Friend_Request_Received: 'friend-request-received',
    Friend_Completed_Game: 'friend-completed-game',
    Mutual_Friends_Connected: 'mutual-friends-connected',
    Request: 'request',
    Game: 'game',
    My_Post_Comment: 'my-post-comment',
    Admin_Profile_Edit: 'admin-profile-edit',
    My_Tg_Offer: 'my-tg-offer',
    Offer: 'offer',
    Event: 'event',
    Message_New: 'message.new',
    My_Tg_Groups: 'my-tg-groups',
    Map: 'map',
    Faq: 'faq',
    referralJoined: 'referral-joined',
    clubUpdate: 'club-update',
    GAME_REMINDER: 'game-reminder',
    TIER_REVISION: 'tier-revision',
    BENEFIT: 'benefit',
    FRIEND_FAVORITE_CLUB: 'friend-favorite-club',
    PEGBOARD: 'pegboard',
    NEW_CLUB_USER: 'new-club-user',
    MUTED_CLUB_REMINDER: 'muted-club-reminder',
    NGV_UPDATE: 'ngv-update',
    UPDATE_EMAIL: 'update-email',
    COUPLE_JOINED: 'couple-joined',
    FEMALE_MEMBER_JOINED: 'female-member-joined',
    MY_TG_GROUP: 'my-tg-group',
    CLUB_MUTED: 'club-unmuted',
    ADMIN_NOTIFICATION: 'admin-notification',
    OFFLINE_GAME_LOGGED: 'offline-game-logged',
    REQUEST_CHAT_NEW_MESSAGE: 'request-chat-new-message',
    REQUEST_CHAT: 'request-chat',
};
