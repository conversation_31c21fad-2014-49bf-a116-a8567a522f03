import React, { useContext, useEffect, useState } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    SafeAreaView,
    Dimensions,
    Linking,
    StyleSheet,
    Alert,
    Platform,
    ImageBackground,
    ActivityIndicator,
} from 'react-native';
import Form from '../FormContext';
import * as yup from 'yup';
import Input from '../../components/fields/Input';
import PhoneNumberInput from '../../components/fields/PhoneNumberInput';
import { GreenButton } from '../../components/buttons';
import auth from '@react-native-firebase/auth';
import ButtonSelect from '../../components/fields/ButtonSelect';
import { validateEmail, validateUsername } from '../../utils/validation';
import { useNavigation } from '@react-navigation/native';
import {
    basicValidationSchema,
    passwordValidationSchema,
    confirmEmailSchema,
    confirmPasswordSchema,
} from '../../utils/validation/validationSchemas';
import { colors } from '../../theme/theme';
import { createAccountUrl, sendVerificationCodeUrlV2 } from '../../service/EndPoint';
import validatePhone from '../../utils/validation/validatePhone';
import CheckboxNew from '../../components/fields/CheckboxNew';
import Checkbox from '../../components/fields/Checkbox';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import UserNameIcon from '../../assets/images/username.svg';
import EmailIcon from '../../assets/images/email-signup.svg';
import PasswordIcon from '../../assets/images/password.svg';
import TGCustomModalView from '../../components/modals/TGCustomModalView';
import PasswordModal from '../../components/modals/PasswordModal';
import TealButton from '../../components/buttons/TealButton';
import { fetcher } from '../../service/fetcher';
import Config from 'react-native-config';
import WebViewModal from '../../components/modals/WebViewModal';

import RegisterScreenContainer from '../../components/layout/RegisterScreenContainer';
import constants from '../../utils/constants/constants';
const CleverTap = require('clevertap-react-native');

const { width } = Dimensions.get('window');

export default function CreateAccountForm() {
    const [errors, setErrors] = useState({});
    const navigation = useNavigation();
    const [loading, setLoading] = useState();
    const [showModal, setShowModal] = useState(false);
    const [phoneNumber, setPhoneNumber] = useState('');
    const [isVerified, setVerified] = useState(false);
    const [loadingApi, setLoadingApi] = useState(false);
    const [webViewVisible, setWebViewVisible] = useState(false);
    const [webViewUrl, setWebViewUrl] = useState('');

    // useEffect(() => setLoading(false))

    const openWebView = (url) => {
        setWebViewUrl(url);
        setWebViewVisible(true);
    };

    async function sendVerificationCode(phone) {
        return await fetcher({
            endpoint: sendVerificationCodeUrlV2,
            method: 'POST',
            body: { phone, _d: '__mObiLE' },
        });
    }

    async function onSubmit(account) {
        if (
            account?.email.toLowerCase() !== account?.confirmEmail.toLowerCase() ||
            account?.password !== account?.confirmPassword
        ) {
            setErrors({
                confirmEmail: account?.email !== account?.confirmEmail && 'Emails do not match',
                confirmPassword: account?.password !== account?.confirmPassword && 'Password do not match',
            });
        } else if (account?.accept_Terms) {
            setLoading(true);
            //setShowModal(true)

            const phone = `${account.phone.dialCode}${account.phone.unmaskedPhoneNumber.trim()}`;

            const emailValid = await validateEmail(account.email);
            const phoneValid = await validatePhone(phone);

            const usernameValid = await validateUsername(account.username);

            console.log('usernameValid', usernameValid);

            if (emailValid && usernameValid && phoneValid) {
                setErrors({
                    confirmEmail: ' ',
                    confirmPassword: ' ',
                });
                setShowModal(true);
                setLoading(false);
                setPhoneNumber(phone);
                await sendVerificationCode(phone).catch(console.log);
            } else {
                setLoading(false);
                setErrors({
                    email:
                        !emailValid &&
                        'Email already exists. If you have already registered, please log in with your email and password. If you forgot your password, use the link below to reset it. To continue registration, enter another email',
                    username: !usernameValid && 'Username is already taken. Please choose another username',
                    phone: !phoneValid && 'Mobile number is already taken. Please choose another mobile number',
                });
            }
        } else {
            // Alert.alert('You must agree to our Terms of Use and policies in order to proceed further')
            Alert.alert('', 'You must agree to our Terms of Use and policies in order to proceed further', [
                { text: 'OK', onPress: () => console.log('OK Pressed') },
            ]);
        }
    }

    async function submitCode(form) {
        console.log('submitCode...', isVerified, form?.code);
        if (isVerified) {
            CleverTap.recordEvent(constants.CLEVERTAP.EVENTS.PHONE_NUMBER_VERIFIED_STEP_1)
            setVerified(false);
            setShowModal(false);
            navigation.reset({
                index: 0,
                routes: [{ name: 'Personal Profile Form' }],
            });
        } else if (form?.code && form?.code?.length > 5) {
            //setVerified(true)
            setLoadingApi(true);
            createAccount(form);
        } else {
            setLoadingApi(false);
            setErrors({ code: 'Please enter a correct verification code' });
        }
    }

    async function createAccount(account) {
        try {
            //  setLoading(true);
            const { code, phone } = account;
            const phoneNumber = `${phone?.dialCode}${phone?.unmaskedPhoneNumber.trim()}`;
            delete account?.accept_Terms;

            console.log('request->', {
                ...account,
                phone: phoneNumber,
                phone_number_details: phone,
            });

            CleverTap.recordEvent(constants.CLEVERTAP.EVENTS.PHONE_NUMBER_VERIFIED_STEP_1);

            fetcher({
                endpoint: createAccountUrl,
                method: 'POST',
                body: {
                    ...account,
                    phone: phoneNumber,
                    phone_number_details: phone,
                },
            }).then((response) => {
                console.log('response', response);
                setLoading(false);
                const { user, status, message } = response;
                if (status && status == 1) {
                    setLoadingApi(false);
                    auth().signInWithCustomToken(user?.loginToken);
                    setVerified(true);
                    CleverTap.recordEvent(constants.CLEVERTAP.EVENTS.VERIFY_OTP);
                } else {
                    setLoadingApi(false);
                    setErrors({ code: message });
                }
            });
        } catch (error) {
            console.log('🚀 ~ createAccount ~ error:', error);
        }
    }

    console.log('object ->', errors);

    return (
        <ImageBackground
            imageStyle={{ resizeMode: 'stretch' }}
            source={require('../../assets/images/signup-background.jpg')}
            style={{ flex: 1 }}>
            <RegisterScreenContainer
                currentStep={1}
                marginBottom={10}
                onBackPress={() => {
                    navigation.goBack();
                }}>
                <View
                    style={{
                        flex: 1,
                    }}>
                    <Form
                        onSubmit={onSubmit}
                        customErrors={errors}
                        initialValues={{
                            username: '',
                            email: '',
                            confirmEmail: '',
                            password: '',
                            confirmPassword: '',
                            opt_in_email: true,
                            accept_Terms: false,
                            phone: { dialCode: '+1', unmaskedPhoneNumber: '' },
                        }}
                        validationSchema={{
                            ...basicValidationSchema,
                            ...confirmEmailSchema,
                            ...confirmPasswordSchema,
                            password: passwordValidationSchema,
                        }}>
                        <KeyboardAwareScrollView
                            bounces={false}
                            showsVerticalScrollIndicator={false}
                            contentContainerStyle={{
                                justifyContent: 'space-between',
                                paddingHorizontal: 25,
                                paddingTop: 20,
                            }}>
                            <Input
                                highLightText="Username*"
                                name="username"
                                placeholder="Username*"
                                TextIcon={UserNameIcon}
                                placeholderColor={colors.fadeBlack}
                                inputStyle={{
                                    fontSize: 14,
                                    color: colors.lightBlack,
                                    paddingLeft: 30,
                                    marginTop: Platform.OS === 'ios' ? 0 : -5,
                                }}
                            />

                            <Input
                                highLightText="Email*"
                                name="email"
                                placeholder="Email*"
                                TextIcon={EmailIcon}
                                placeholderColor={colors.fadeBlack}
                                inputStyle={{
                                    fontSize: 14,
                                    color: colors.lightBlack,
                                    paddingLeft: 30,
                                    marginTop: Platform.OS === 'ios' ? 0 : -5,
                                }}
                            />

                            <Input
                                copyHidden={true}
                                highLightText="Confirm Email*"
                                name="confirmEmail"
                                placeholder="Confirm Email*"
                                TextIcon={EmailIcon}
                                placeholderColor={colors.fadeBlack}
                                inputStyle={{
                                    fontSize: 14,
                                    color: colors.lightBlack,
                                    paddingLeft: 30,
                                    marginTop: Platform.OS === 'ios' ? 0 : -5,
                                }}
                            />

                            <PhoneNumberInput
                                highLightText="Mobile Number*"
                                bgColor="transparent"
                                type="createProfile"
                                placeholder="Mobile Number*"
                                textInputStyle={{ fontSize: 14, color: colors.lightBlack }}
                            />

                            <View style={{ marginTop: 5 }}>
                                <Input
                                    highLightText="Password*"
                                    name="password"
                                    placeholder="Password*"
                                    TextIcon={PasswordIcon}
                                    placeholderColor={colors.fadeBlack}
                                    note="Note: Please ensure your password contains a minimum of 8 characters, 1 capital letter, 1 lowercase letter, and 1 special character"
                                    inputStyle={{
                                        fontSize: 14,
                                        color: colors.lightBlack,
                                        paddingLeft: 30,
                                        marginTop: Platform.OS === 'ios' ? 0 : -5,
                                    }}
                                />
                            </View>

                            <Input
                                highLightText="Confirm Password*"
                                name="confirmPassword"
                                placeholder="Confirm Password*"
                                TextIcon={PasswordIcon}
                                placeholderColor={colors.fadeBlack}
                                inputStyle={{
                                    fontSize: 14,
                                    color: colors.lightBlack,
                                    paddingLeft: 30,
                                    marginTop: Platform.OS === 'ios' ? 0 : -5,
                                }}
                            />

                            <View style={{ marginVertical: 20 }}>
                                <Checkbox
                                    type={'createaccount'}
                                    checkStyle={{ height: 22, width: 22, borderRadius: 2 }}
                                    textStyle={{
                                        fontSize: 12,
                                        fontFamily: 'Ubuntu-Regular',
                                        color: colors.lightBlack,
                                        lineHeight: 16,
                                    }}
                                    name="opt_in_email"
                                    label="Please send me the monthly site updates including events and benefits."
                                />

                                <Checkbox
                                    textStyle={{ fontSize: 12, fontFamily: 'Ubuntu-Regular', color: colors.lightBlack }}
                                    checkStyle={{ height: 22, width: 22, borderRadius: 2 }}
                                    type={'createaccount'}
                                    name="accept_Terms"
                                    label="I agree to the Thousand Greens Terms of Use, Privacy Policy, & Copyright Dispute Policy"
                                    showpolicy={true}
                                    child={
                                        <Text
                                            style={[
                                                {
                                                    paddingLeft: 10,
                                                    fontFamily: 'Ubuntu-Regular',
                                                    color: colors.lightBlack,
                                                    fontSize: 12,
                                                    lineHeight: 16,
                                                },
                                            ]}>
                                            I agree to the Thousand Greens{' '}
                                            <Text
                                                style={{ textDecorationLine: 'underline' }}
                                                onPress={() => openWebView('https://thousandgreens.com/terms')}>
                                                Terms of Use
                                            </Text>
                                            <Text>{', '}</Text>
                                            <Text
                                                style={{ textDecorationLine: 'underline' }}
                                                onPress={() => openWebView('https://thousandgreens.com/privacy')}>
                                                Privacy Policy
                                            </Text>
                                            <Text> & </Text>
                                            <Text
                                                style={{ textDecorationLine: 'underline' }}
                                                onPress={() => openWebView('https://thousandgreens.com/copyright')}>
                                                Copyright Dispute Policy
                                            </Text>
                                        </Text>
                                    }
                                />
                            </View>
                            <View style={{ alignItems: 'center', paddingVertical: 20 }}>
                                {console.log('loading', loading)}
                                {loading ? (
                                    <ActivityIndicator color={colors.darkteal} />
                                ) : (
                                    <GreenButton
                                        width={'90%'}
                                        text="Get Verification Code"
                                        loading={loading}
                                        type="create account"
                                    />
                                )}

                                <View style={{ paddingTop: 20, flexDirection: 'row', alignSelf: 'center' }}>
                                    <Text
                                        style={{
                                            fontFamily: 'Ubuntu-Regular',
                                            marginRight: 10,
                                            color: colors.lightBlack,
                                            fontSize: 14,
                                        }}>
                                        Already have an account?
                                    </Text>
                                    <TouchableOpacity
                                        onPress={() => {
                                            navigation.navigate('Login');
                                        }}>
                                        <Text
                                            style={{
                                                color: colors.darkteal,
                                                fontFamily: 'Ubuntu-Medium',
                                                fontSize: 14,
                                                marginLeft: -5,
                                            }}>
                                            Login
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </KeyboardAwareScrollView>

                        <TGCustomModalView isVisible={showModal} animationType={'fade'}>
                            <PasswordModal
                                closeModal={() => {
                                    setErrors({ code: null });
                                    setShowModal(false);
                                }}
                                phoneNumber={phoneNumber}
                                errorsState={[errors, setErrors]}
                                isVerified={isVerified}
                                onValueChanged={submitCode}
                                loading={loadingApi}
                                resendCode={(form) => {
                                    CleverTap.recordEvent(constants.CLEVERTAP.EVENTS.PHONE_NUMBER_VERIFIED_STEP_1);
                                    const phone = `${form.phone.dialCode}${form.phone.unmaskedPhoneNumber.trim()}`;
                                    sendVerificationCode(phone).catch(console.log);
                                }}
                            />
                        </TGCustomModalView>
                    </Form>
                </View>
            </RegisterScreenContainer>

            <WebViewModal
                visible={webViewVisible}
                url={webViewUrl}
                onClose={() => setWebViewVisible(false)}
            />
        </ImageBackground>
    );
}

const styles = StyleSheet.create({
    hyperlinkTxt: {
        color: colors.darkteal,
        // textDecorationLine:'underline'
    },
});
