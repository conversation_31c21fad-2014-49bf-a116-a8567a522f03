import React, { useContext, useState, useEffect } from 'react';
import { SafeAreaView, View, Text } from 'react-native';
import Form from '../FormContext';
import ClubSelect from '../../components/fields/ClubSelect';
import Select from '../../components/fields/Select';
import Spacer from '../../components/layout/Spacer';
import * as yup from 'yup';
import { colors } from '../../theme/theme';
import DateRangeField from '../../components/fields/DateRangeField';
import Checkbox from '../../components/fields/Checkbox';
import TextArea from '../../components/fields/TextArea';
import Input from '../../components/fields/Input';
import GreenButton from '../../components/buttons/GreenButton';
import CancelButton from '../../components/buttons/CancelButton';
import { AuthContext } from '../../context/AuthContext';
import useClient from '../../hooks/useClient';
import Moment from 'moment';
import MultiCheckbox from '../../components/fields/MultiCheckbox';

const GET_CLUB = `
query getCourse($id: Int!) {
    courses_by_pk(id: $id) {
      closurePeriods
      guest_time_restrictions
    }
}
`;

const FieldHeader = ({ label }) => (
    <Text
        style={{
            fontFamily: 'Ubuntu-Light',
            color: colors.darkgray,
            lineHeight: 34,
        }}>
        {label}
    </Text>
);

export default function RequestForm({
    dateRangeModalState,
    onSubmit,
    onCancel,
    request,
    loading,
    club,
    noRelevantMembersError
}) {
    const [dateRangeModal, setDateRangeModal] = dateRangeModalState;
    const { user } = useContext(AuthContext);
    const [requestForm, setRequestForm] = useState({});
    const [showPrivateNetworkOnly, setShowPrivateNetworkOnly] = useState(false);
    const [closurePeriods, setClosurePeriods] = useState();
    const [disabledDates, setDisabledDates] = useState([]);
    const client = useClient();
    const [privateNetworkClubIds, setPrivateNetworkClubIds] = useState();
    function capitalizeString(string) {
        return string ? `${string[0].toUpperCase()}${string.slice(1)}` : '';
    }

    function getInitialValues() {
        if (request) {
            return {
                club_id: request.club_id,
                number_of_players: `${request.number_of_players}`,
                accompanied_only: request.criteria.accompanied_only,
                playAsCouple: request.criteria.playAsCouple,
                gender: capitalizeString(request.criteria.gender),
                pace: request.criteria.pace,
                handicap: request.criteria.handicap,
                englishFluency: request.criteria.englishFluency,
                all_ages: request.criteria.all_ages ? 'No' : 'Yes',
                sendToPrivateNetwork: request.criteria.sendToPrivateNetwork,
                start_date: request.start_date,
                end_date: request.end_date,
                message: request.message,
            };
        } else {
            return {
                club_id: club && club.id,
                number_of_players: 1,
                accompanied_only: true,
                playAsCouple: false,
                gender: user.gender === 'male' ? 'Male' : 'Both',
                pace: ['fast', 'average', 'leisure'],
                handicap: ['< 5', '5-10', '> 10'],
                englishFluency: ['native', 'fluent', 'understandable', 'basic'],
                all_ages: 'No',
                sendToPrivateNetwork: false,
            };
        }
    }

    //Show private network only effect
    useEffect(() => {
        if (requestForm.club_id && !closurePeriods) {
            client
                .request(GET_CLUB, {
                    id: requestForm.club_id,
                })
                .then(({ courses_by_pk: club }) => {
                    //      //console.log({club});
                    setClosurePeriods(club.closurePeriods);
                });
        }
    }, [requestForm]);

    useEffect(() => {
        if (closurePeriods && closurePeriods.length > 0) {
            const closurePeriodDates = closurePeriods
                .map(({ from, to }) => {
                    const startDate = Moment(from);
                    const endDate = Moment(to);
                    
                    // Check if dates are valid
                    if (!startDate.isValid() || !endDate.isValid()) {
                        console.warn('Invalid date in closure period:', { from, to });
                        return [];
                    }
                    
                    const nDaysBetween = endDate.diff(startDate, 'd');
                    
                    // Prevent creating arrays that are too large
                    if (nDaysBetween > 365) { // Limit to 1 year maximum
                        console.warn('Closure period too large, limiting to 365 days:', { from, to });
                        return [];
                    }
                    
                    try {
                        const dates = new Array(nDaysBetween)
                            .fill('')
                            .map((_, index) => {
                                return Moment(from)
                                    .add(index, 'days')
                                    .format('MM/DD/YYYY');
                            });
                        return dates;
                    } catch (error) {
                        console.warn('Error creating closure period dates array:', error);
                        return [];
                    }
                })
                .flat();

            setDisabledDates(closurePeriodDates);
        }
    }, [closurePeriods]);

    return (
        <Form
            onSubmit={onSubmit}
            onUpdate={setRequestForm}
            validationSchema={{
                club_id: yup.string().required('Club is required'),
                number_of_players: yup
                    .string()
                    .required('Number of players is required'),
                start_date: yup.string().required('Date range is required'),
                end_date: yup.string().required('Date range is required'),
            }}
            initialValues={getInitialValues()}>
            <Text
                style={{ fontFamily: 'Ubuntu-Regular', color: colors.darkgray }}>
                Club
            </Text>
            {club || request ? (
                <Text
                    style={{
                        fontFamily: 'Ubuntu-Regular',
                        marginTop: 5,
                        fontSize: 16,
                        color: colors.darkteal,
                    }}>
                    {club ? club.name : request.club.name}
                </Text>
            ) : (
                <ClubSelect
                    name="club_id"
                    placeholder=""
                    clubIdOnly
                    user={user}
                    type="request"
                    privateNetworkClubIds={privateNetworkClubIds}
                />
            )}
            <Text
                style={{
                    fontFamily: 'Ubuntu-Regular',
                    fontSize: 16,
                    marginVertical: 20,
                }}>
                Send Requests to
            </Text>
            <View
                style={{
                    backgroundColor: colors.lightgray,
                    padding: 15,
                    borderRadius: 10,
                    paddingBottom: 30,
                }}>
                <View>
                    <FieldHeader label="Gender" />
                    <Select
                        name="gender"
                        options={
                            user.gender === 'male'
                                ? ['Both', 'Male']
                                : ['Both', 'Female']
                        }
                    />
                    <FieldHeader label="Pace" />
                    <MultiCheckbox
                        name="pace"
                        options={['fast', 'average', 'leisure']}
                    />
                    <FieldHeader label="Golf index" />
                    <MultiCheckbox
                        name="handicap"
                        options={['< 5', '5-10', '> 10']}
                    />
                    <FieldHeader label="English Fluency" />
                    <MultiCheckbox
                        minWidth="50%"
                        name="englishFluency"
                        options={[
                            'native',
                            'fluent',
                            'understandable',
                            'basic',
                        ]}
                    />
                    <FieldHeader label="Age Range" />
                </View>

                <Select name="all_ages" options={['Yes', 'No']} />
                {requestForm.all_ages === 'Yes' && (
                    <>
                        <Input name="min_age" placeholder="Min. Age" />
                        <Input name="max_age" placeholder="Max. Age" />
                    </>
                )}
            </View>
            <Spacer />
            <Select
                name="number_of_players"
                placeholder="Number Of People"
                options={['1', '2', '3']}
            />
            <Spacer />
            <DateRangeField
                setModal={setDateRangeModal}
                disabledDates={disabledDates}
            />
            <Spacer />
            <TextArea name="message" />
            <Spacer />
            <Checkbox name="accompanied_only" label="Accompanied Only" />
            {user.playAsCouple && (
                <Checkbox name="playAsCouple" label="Play as Couple" />
            )}
            {showPrivateNetworkOnly && (
                <Checkbox
                    name="sendToPrivateNetwork"
                    label="Private Network only?"
                />
            )}
            <SafeAreaView>
                {noRelevantMembersError &&
                    <Text style={{ fontFamily: 'Ubuntu-Light', color: 'red', textAlign: 'center' }}>
                        There are no qualified hosts for this request. Please adjust your filter criteria and try again
                    </Text>
                }
                <View
                    style={{
                        flexDirection: 'row',
                        marginTop: 20,
                        paddingBottom: 30,
                    }}>
                    <View style={{ flexGrow: 1, alignItems: 'center' }}>
                        <CancelButton onPress={() => onCancel()} />
                    </View>
                    <GreenButton
                        text={request ? 'SAVE REQUEST' : 'POST REQUEST'}
                        loading={loading}
                    />
                </View>
            </SafeAreaView>
        </Form>
    );
}
