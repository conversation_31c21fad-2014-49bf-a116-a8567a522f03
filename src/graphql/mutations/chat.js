const INSERT_REQUEST_CHAT = `
mutation createRequestChat($chat: request_chat_insert_input!) {
    insert_request_chat_one(object: $chat) {
      sendbird_channel_id
    }
}
`;
const UPDATE_REQUEST_CHAT = `
mutation updateRequestChat($club_member_id: uuid!, $request_id: uuid!) {
    update_request_chat_by_pk(pk_columns: {club_member_id: $club_member_id, request_id: $request_id}, _set: {has_messages: true}) {
      request_id
    }
}
`;
const UPDATE_REQUESTER_CHAT = `
mutation updateRequestChat($club_member_id: uuid!, $request_id: uuid!) {
    update_request_chat_by_pk(pk_columns: {club_member_id: $club_member_id, request_id: $request_id}, _set: {requester_has_message: true}) {
      request_id
    }
}
`;

const GET_REQUEST_CHAT = `query getRequestChat($requestId: uuid!, $channelId: String!) {
  request_chat(where: {request_id: {_eq: $requestId}, stream_channel_id: {_eq: $channelId}}) {
    club_member_id
  }
}`

export { INSERT_REQUEST_CHAT, UPDATE_REQUEST_CHAT, UPDATE_REQUESTER_CHAT, GET_REQUEST_CHAT };
