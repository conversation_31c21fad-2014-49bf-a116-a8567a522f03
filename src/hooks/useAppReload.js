import { useState, useEffect, useCallback } from 'react';
import appStateManager from '../utils/AppStateManager';
import RNRestart from 'react-native-restart';

export default function useAppReload() {
    const [isReloading, setIsReloading] = useState(false);
    const [shouldForceReload, setShouldForceReload] = useState(false);

    // Handle app state manager events
    const handleAppStateEvent = useCallback((event, data) => {
        console.log('useAppReload: Received event:', event, data);
        
        switch (event) {
            case 'forceReload':
                setShouldForceReload(true);
                break;
            default:
                break;
        }
    }, []);

    // Initialize app state manager
    useEffect(() => {
        appStateManager.init();
        
        // Add listener for app state events
        const removeListener = appStateManager.addListener(handleAppStateEvent);
        
        return () => {
            removeListener();
            appStateManager.cleanup();
        };
    }, [handleAppStateEvent]);

    // Handle automatic restart - no user interaction required
    const handleForceReload = useCallback(async () => {
        try {
            console.log('useAppReload: Starting automatic app restart...');
            setIsReloading(true);

            // Clear all app state and cache
            await clearAppState();

            // Small delay to ensure cache clearing is complete, then restart
            setTimeout(() => {
                console.log('useAppReload: Restarting app automatically...');
                RNRestart.Restart();
            }, 1000);

        } catch (error) {
            console.error('useAppReload: Error during automatic restart:', error);
            // Fallback to direct restart if cache clearing fails
            RNRestart.Restart();
        }
    }, []);

    // Clear app state and cache
    const clearAppState = async () => {
        try {
            // Import AsyncStorage dynamically to avoid circular dependencies
            const AsyncStorage = require('@react-native-async-storage/async-storage').default;
            
            // Clear all app data except authentication
            const keys = await AsyncStorage.getAllKeys();
            const keysToKeep = [
                'user_token',
                'user_data',
                'auth_state',
                'background_start_time'
            ];
            
            const keysToRemove = keys.filter(key => !keysToKeep.includes(key));
            
            if (keysToRemove.length > 0) {
                await AsyncStorage.multiRemove(keysToRemove);
                console.log('useAppReload: Cleared', keysToRemove.length, 'cache keys');
            }
            
            // Clear Apollo cache if available
            if (global.apolloClient) {
                try {
                    await global.apolloClient.clearStore();
                    console.log('useAppReload: Apollo cache cleared');
                } catch (error) {
                    console.warn('useAppReload: Could not clear Apollo cache:', error);
                }
            }
            
            // Clear Stream chat client if available
            if (global.streamClient) {
                try {
                    await global.streamClient.disconnectUser();
                    console.log('useAppReload: Stream client disconnected');
                } catch (error) {
                    console.warn('useAppReload: Could not disconnect Stream client:', error);
                }
            }
            
        } catch (error) {
            console.error('useAppReload: Error clearing app state:', error);
            throw error;
        }
    };

    // Check if app should reload on mount
    useEffect(() => {
        const checkInitialReload = async () => {
            if (appStateManager.shouldReload()) {
                console.log('useAppReload: App should reload on mount');
                setShouldForceReload(true);
            }
        };
        
        checkInitialReload();
    }, []);

    return {
        isReloading,
        shouldForceReload,
        handleForceReload,
        clearAppState
    };
} 