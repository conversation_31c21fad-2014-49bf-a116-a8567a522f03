import { MapClub, MapClubDetail, Map<PERSON>lubO<PERSON>s, Offer, OfferDetail } from '.';

export type RootStackParamList = {
    RequestScreen: undefined;
    'Create Request': {
        club?: MapClubDetail | null;
        onGoBack?: (data: any) => void;
        request?: any;
        category?: any;
        memberCount?: number;
        setSelectedClubState?: (club: any) => void;
    };
    ChatDetails: {
        type?: string;
        request_id: string;
        game_id: number;
        streamChannelId: string;
        requestor_user_id: string;
        requestor_full_name: string;
        host_user_id: string;
        has_messages: boolean;
        isDeletedUser?: boolean;
    };
    RequestConfirmScreen: {
        popupType?: string;
        game_id?: number;
        requestor_full_name?: string;
        acceptRequest?: boolean;
        chatAgainCallBack?: () => void;
        request?: any;
        handleYesButton?: () => void;
        declineRequest?: boolean;
        handleDeclineRequest?: () => void;
        deleteRequest?: boolean;
        request_id?: string;
        type?: string;
        callBack?: () => void;
        showReason?: boolean;
        hostData?: any[];
    };
    RequestDetailScreen: {
        request: any;
        type: string;
        isMyRequest: boolean;
        requestDetailType?: string;
    };
    RequestHistoryScreen: undefined;
    LogFinalPopup: undefined;
    'Recommended Club': {
        recommendedClubs: any[];
        requestedClub: any;
    };
    WarningScreen: undefined;
    MapDetails: {
        comeFromOffer?: boolean;
        prevScreenCallBack?: () => void;
        mapView?: string;
    } | undefined;
    MyTGFriendsScreen: undefined;
    MyFriendInClubScreen: {
        club?: MapClubDetail;
        setSelectedClub?: (club: MapClub[]) => void;
    };
    PlayedFriendScreen: {
        club?: MapClubDetail;
        setSelectedClub?: (club: MapClub[]) => void;
    };
    TgGroupMembersScreen: {
        club?: MapClubDetail;
        setSelectedClub?: (club: MapClub[]) => void;
    };
    CreateRequestMap: {
        club?: MapClubDetail;
        setSelectedClub?: (club: MapClub[]) => void;
        createOneToOneRequest?: boolean;
        friend_id?: string;
        screen?: string;
    };
    UserProfileScreen: {
        selectedUser: {
            id: string;
        };
        callBack?: () => void;
        isComeFromChat?: boolean;
    };
    MessageScreen: {
        channel?: any;
        screen?: string;
    };
    GameReview: {
        club: MapClub;
    };
    'Image Show': {
        imageUri: string;
    };
    BottomTabNavigation: undefined;
    'Edit Offer': {
        offer: MapClubOffers | OfferDetail | null;
        refresh: () => void;
    };
    'DeleteChannelConfirmationPopup': {
        handleYesButton?: () => void;
        handleFirstBtnPress?: () => void;
        popupHeader?: string;
        popupTitle?: string;
        firstBtnLabel?: string;
        secondBtnLabel?: string;
        popupSubText?: string;
    };
    'Request Against Offer': {
        offer: MapClubOffers | OfferDetail | Offer | null;
        callBack?: () => void;
    };
    'Create Offer': {
        selectClubForOffer?: MapClubDetail | null;
        createFrom?: string;
        my_tg_group_id?: string | null;
        my_tg_group?: string;
        callBack?: () => void;
    };
    'OfferDetails': {
        offerID: number;
        prevScreenCallBack?: () => void;
    };
    'Offers': {
        club: MapClub;
    };
    ShowUserDp: {
        profilePhoto: string;
    };
    UsersAllGameReviewScreen: {
        golferId: string;
        userId: string;
    };
};
