import React from 'react';
import AuthProvider from '../context/AuthContext';
import ClubsScreen from '../screens/complete-profile/ClubsScreen';
import ProfileSettingsScreen from '../screens/complete-profile/ProfileSettingsScreen';
import AboutTokensScreen from '../screens/complete-profile/AboutTokensScreen';
import AccountSettingsScreen from '../screens/profile/AccountSettingsScreen';
import BlockerScreen from '../screens/register/BlockerScreen';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

const Stack = createNativeStackNavigator();

export default function CompleteProfileStack({ user, token, refreshUser }) {
    return (
        <AuthProvider user={{ user, token, refreshUser }}>
            <Stack.Navigator
                screenOptions={{ headerShown: false }}>
                {
                    !user?.visited_account_settings && user?.profile_complete
                        && user?.membership_active
                        ?
                        <Stack.Screen
                            name="Account Settings"
                            component={
                                AccountSettingsScreen
                            }
                        />
                        :
                        <Stack.Screen
                            name="BlockerScreen"
                            component={BlockerScreen}
                        />
                }
                <Stack.Screen
                    name="Complete Profile Clubs"
                    component={ClubsScreen}
                />
                <Stack.Screen
                    name="Complete Profile Settings"
                    component={ProfileSettingsScreen}
                />
                <Stack.Screen
                    name="About Tokens"
                    component={AboutTokensScreen}
                />
            </Stack.Navigator>
        </AuthProvider>
    );
}
