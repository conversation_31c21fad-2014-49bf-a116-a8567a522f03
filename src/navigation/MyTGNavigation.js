import TGChat from '../screens/my-TG-Stream-Chat/view/TGChat';
import ChatIconTealWhiteBg from '../assets/svg/ChatIconTealWhiteBg.svg';
import ChatIconGrayWhiteBg from '../assets/svg/ChatIconGrayWhiteBg.svg';
import FeedIconGreyWhiteBg from '../assets/svg/FeedIconGreyWhiteBg.svg';
import FeedIconTealWhiteBg from '../assets/svg/FeedIconTealWhiteBg.svg';
import FriendIconGreyWhiteBg from '../assets/svg/FriendIconGreyWhiteBg.svg';
import FriendIconTealWhiteBg from '../assets/svg/FriendIconTealWhiteBg.svg';
import PeopleIconGreyWhiteBg from '../assets/svg/PeopleIconGreyWhiteBg.svg';
import PeopleIconTealWhiteBg from '../assets/svg/PeopleIconTealWhiteBg.svg';
import { KeyboardAvoidingView, Pressable, View } from 'react-native';
import { colors } from '../theme/theme';
import { Size, Spacing, Typography } from '../utils/responsiveUI';
import MyTGFriendsScreen from '../screens/my-TG-friends/view/MyTGFriendsScreen';
import { useContext, useEffect, useState } from 'react';
import TGText from '../components/fields/TGText';
import { StyleSheet } from 'react-native';
import GroupScreen from '../screens/myTgGroup/groups/view/Group';
import { useIsFocused } from '@react-navigation/native';
import { GlobalContext } from '../context/contextApi';
import { StreamChatContext } from '../context/StreamChatContext';
const CleverTap = require('clevertap-react-native');

const SCREENS_NAME = ['Chat', 'Groups', 'Friends', 'Feed'];

const MyTGBottomTab = (props) => {
    const { state, actions } = useContext(GlobalContext);
    const { unreadcount } = useContext(StreamChatContext);
    const { route } = props;
    const [selectedTab, setSelectedTab] = useState(
        SCREENS_NAME[route?.params?.mainTab || 3],
    );
    const [selectedPosition, setSelectedPosition] = useState(
        route?.params?.mainTab ?? 0,
    );
    const isFocused = useIsFocused();
    useEffect(() => {
        if (route?.params?.mainTab) {
            setSelectedTab(SCREENS_NAME[route?.params.mainTab]);
            setSelectedPosition(route?.params.mainTab);
        }
    }, []);

    useEffect(() => {
        setSelectedPosition(state?.currentTab);
        setSelectedTab(SCREENS_NAME[state?.currentTab]);
    }, [isFocused, state?.currentTab]);

    const tabScreen = (name) => {
        switch (name) {
            case SCREENS_NAME[0]:
                return <TGChat />;
            case SCREENS_NAME[1]:
                return <GroupScreen />;
            case SCREENS_NAME[2]:
                return <MyTGFriendsScreen routeData={route?.params} />;
            case SCREENS_NAME[3]:
            default:
                return <TGChat />;
        }
    };

    const tabIcon = (name) => {
        const selected = selectedPosition === SCREENS_NAME.indexOf(name);

        switch (name) {
            case SCREENS_NAME[0]:
                return selected ? ChatIconTealWhiteBg : ChatIconGrayWhiteBg;
            case SCREENS_NAME[1]:
                return selected ? PeopleIconTealWhiteBg : PeopleIconGreyWhiteBg;
            case SCREENS_NAME[2]:
                return selected ? FriendIconTealWhiteBg : FriendIconGreyWhiteBg;
            case SCREENS_NAME[3]:
                return selected ? FeedIconTealWhiteBg : FeedIconGreyWhiteBg;
            default:
                return selected ? ChatIconTealWhiteBg : ChatIconGrayWhiteBg;
        }
    };

    return (
        <>
            <KeyboardAvoidingView
                style={{ flex: 1 }}
                behavior={Platform.OS === 'ios' ? null : 'height'}
                keyboardVerticalOffset={Platform.OS === 'android' && -30}>
                <View style={{ flex: 1 }}>
                    <View style={{ flex: 1 }}>{tabScreen(selectedTab)}</View>
                    <View
                        style={{
                            flexDirection: 'row',
                            height:
                                Platform.OS === 'android'
                                    ? Size.SIZE_60
                                    : Size.SIZE_80,
                        }}>
                        {SCREENS_NAME.map((name, index) => (
                            <TabItem
                                key={index}
                                name={name}
                                Icon={tabIcon(name)}
                                isSelected={selectedPosition === index}
                                action={() => {
                                    setSelectedPosition(index);
                                    setSelectedTab(name);
                                    actions.setCurrentTab(index); // Set current tab in context
                                    actions?.setFriendTabNavigation('All Friends');
                                    CleverTap.recordEvent(name === 'Friends' ? 'My- Friends' : name)
                                }}
                            />
                        ))}
                        {unreadcount ? <View style={styles.redDot} /> : null}
                    </View>
                </View>
            </KeyboardAvoidingView>
        </>
    );
};

export default MyTGBottomTab;

const TabItem = ({ name, Icon, isSelected, action }) => {
    return (
        <Pressable onPress={action} style={styles.container}>
            {isSelected && <View style={styles.lineStyle} />}
            <Icon height={25} width={25} />
            <TGText
                style={[
                    styles.text,
                    {
                        marginTop: 3,
                        color: isSelected ? colors.tealRgb : colors.fadeBlack,
                        fontWeight: isSelected ? '500' : '400',
                    },
                ]}>
                {name}
            </TGText>
        </Pressable>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        paddingBottom: 20,
        paddingTop: 10,
        backgroundColor: colors.white,
        paddingHorizontal: 5,
    },
    text: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
    },
    image: {
        height: 20,
        width: 20,
    },
    lineStyle: {
        position: 'absolute',
        top: 0,
        left: '50%',
        right: 0,
        height: 4,
        borderRadius: 5,
        backgroundColor: colors.tealRgb,
        width: Size.SIZE_50,
        marginLeft: -25,
    },
    redDot: {
        width: Size.SIZE_8,
        height: Size.SIZE_8,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.errorColor,
        position: 'absolute',
        left: Spacing.SCALE_45,
        top: Spacing.SCALE_8,
    },
});
