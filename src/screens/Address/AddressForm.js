import React, { useContext, useEffect, useState } from 'react';
import {
    View,
    Text,
    TextInput,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    SafeAreaView,
    ActivityIndicator,
    Alert,
    useColorScheme,
    Platform,
    KeyboardAvoidingView,
    StatusBar,
} from 'react-native';

import showToast from '../../components/toast/CustomToast';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import { AuthContext } from '../../context/AuthContext';
import { fetcher } from '../../service/fetcher';
import { UPDATE_USER_ADDRESS } from '../../service/EndPoint';
import { useNavigation } from '@react-navigation/native';
import { getAllCitiesByStateAndCountry, getAllCountries, getAllStatesByCountry } from './action';
import Logout from '../../assets/svg/logout.svg';
import auth from '@react-native-firebase/auth';
import config from '../../config';
import { GlobalContext } from '../../context/contextApi';
import SelectorInput from './SelectorInput';
import { Back } from '../../assets/images/svg';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import LinearGradient from 'react-native-linear-gradient';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

const priorityCountries = ['United States', 'Canada', 'United Kingdom', 'Australia'];

const AddressForm = ({ route }) => {
    const [isEdit, setIsEdit] = useState(route?.params?.screen === 'Profile' ? true : false);
    const [isComeForAddAddress] = useState(route?.params?.type === 'Add Address' ? true : false);
    const { user, refreshUser } = useContext(AuthContext);
    const { actions, state } = useContext(GlobalContext);
    const navigation = useNavigation();
    const colorScheme = useColorScheme(); // Detects light or dark mode
    const [address, setAddress] = useState(user?.stripe_customer_info?.address.line1 || '');
    const [selectedCountry, setSelectedCountry] = useState(null);
    const [selectedState, setSelectedState] = useState(null);
    const [selectedCity, setSelectedCity] = useState(null);
    const [postalCode, setPostalCode] = useState(user?.stripe_customer_info?.address?.postal_code || '');
    const [isValid, setIsValid] = useState(false);
    const [errors, setErrors] = useState({
        postalCode: '',
    });
    const [loading, setLoading] = useState(false);
    const [isShowSimmer, setShowSimmer] = useState(false);

    useEffect(() => {
        setShowSimmer(true);
        getCountries();
    }, []);

    useEffect(() => {
        if (selectedCountry) {
            getStates();
        }
    }, [selectedCountry]);

    useEffect(() => {
        if (selectedState && selectedCountry) {
            getAllCities();
        }
    }, [selectedCountry, selectedState]);

    useEffect(() => {
        if (isEdit) {
            if (selectedCountry) getStates('', true);
        }
    }, [isEdit, selectedCountry]);
    useEffect(() => {
        if (isEdit) {
            if (selectedState && selectedCountry) getAllCities('', true);
            setTimeout(() => {
                setShowSimmer(false);
            }, 2000);
        } else {
            setShowSimmer(false);
        }
    }, [isEdit, selectedState, selectedCountry]);

    const sortCountries = (data) => {
        const sortedData = data.sort((a, b) => {
            if (priorityCountries.includes(a.label) && !priorityCountries.includes(b.label)) {
                return -1;
            }
            if (!priorityCountries.includes(a.label) && priorityCountries.includes(b.label)) {
                return 1;
            }
            return 0; // Maintain the existing order
        });
        return sortedData;
    };

    const getCountries = async (searchText) => {
        let data = await getAllCountries();

        let countries = data?.map((country) => {
            return {
                label: country?.name,
                value: { isoCode: country.iso2, name: country?.name },
            };
        });
        countries = sortCountries(countries);
        if (isEdit) {
            setSelectedCountry(
                countries.find((country) => country.label === user?.stripe_customer_info.address.country).value,
            );
        }
        if (searchText) {
            actions.setCountry(
                countries.filter((country) => country.label.toLowerCase().includes(searchText.toLowerCase())),
            );
        } else {
            actions.setCountry(countries);
        }
    };

    const handleCallBack = (value, type) => {
        if (type === 'country') {
            setSelectedCountry(value);
            setSelectedState(null);
            setSelectedCity(null);
        } else if (type === 'state') {
            setSelectedState(value);
            setSelectedCity(null);
        } else if (type === 'city') {
            setSelectedCity(value);
        }
    };

    const getStates = async (searchText = '', isEdit = false) => {
        let data = await getAllStatesByCountry(selectedCountry?.isoCode);

        let sortedStates = data?.sort((a, b) => {
            let x = a?.name.toLowerCase();
            let y = b?.name.toLowerCase();

            if (x > y) {
                return 1;
            }
            if (x < y) {
                return -1;
            }
            return 0;
        });

        let states = sortedStates?.map((state) => ({
            label: state.name,
            value: state,
        }));
        if (isEdit && user?.stripe_customer_info.address.state) {
            setSelectedState(states.find((state) => state.label === user?.stripe_customer_info.address.state).value);
        }
        if (searchText) {
            let searchedData = states.filter((state) => state.label.toLowerCase().includes(searchText.toLowerCase()));
            actions?.setState(searchedData);
        } else {
            actions?.setState(states);
        }
    };

    const getAllCities = async (searchText = '', isEdit = false) => {
        let data = await getAllCitiesByStateAndCountry(selectedCountry?.isoCode, selectedState?.iso2);

        let cities = data?.map((city) => ({
            label: city.name,
            value: city,
        }));
        if (isEdit && user?.stripe_customer_info.address.city) {
            setSelectedCity(cities.find((city) => city.label === user?.stripe_customer_info.address.city).value);
        }
        if (searchText) {
            let searchedData = cities.filter((city) => city.label.toLowerCase().includes(searchText.toLowerCase()));
            actions?.setCity(searchedData);
        } else {
            actions?.setCity(cities);
        }
    };

    useEffect(() => {
        if (selectedCountry && address && postalCode) {
            setIsValid(true);
            setErrors({ postalCode: '' });
        } else {
            setIsValid(false);
        }
    }, [selectedCountry, address, postalCode]);

    const onClickContinueHandler = () => {
        setLoading(true);
        let body = {
            userId: user?.id,
            address,
            country: selectedCountry?.name,
            postal_code: postalCode,
        };

        if (selectedCity) {
            body = { ...body, city: selectedCity?.name };
        }
        if (selectedState) {
            body = { ...body, state: selectedState?.name };
        }

        if (errors.postalCode) {
            return;
        }

        fetcher({
            endpoint: UPDATE_USER_ADDRESS,
            method: 'POST',
            body,
        })
            .then((res) => {
                setLoading(false);
                if (res?.status) {
                    isEdit ? navigation.goBack() : navigation.navigate(config?.routes?.BOTTOM_TAB_NAVIGATION);
                    refreshUser();
                } else {
                    if (res?.error?.includes('postal')) {
                        setErrors({
                            postalCode: res?.error,
                        });
                    }
                }
            })
            .catch(() => {
                showToast({});
            });
    };

    const logoutUser = async () => {
        navigation.navigate('DeleteChannelConfirmationPopup', {
            handleYesButton: async () => {
                await auth().signOut();
            },
            popupHeader: 'Confirmation',
            popupTitle: 'Are you sure you want to Logout?',
            firstBtnLabel: 'Dismiss',
            secondBtnLabel: 'Logout',
        });
    };

    const addressSimmer = () => {
        return (
            <>
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={{ width: '100%', height: Size.SIZE_42, marginBottom: Spacing.SCALE_10, borderRadius: 8 }}
                />
                <View style={styles.row}>
                    <ShimmerPlaceholder
                        LinearGradient={LinearGradient}
                        style={{ width: '45%', height: Size.SIZE_42, marginBottom: Spacing.SCALE_10, borderRadius: 8 }}
                    />
                    <ShimmerPlaceholder
                        LinearGradient={LinearGradient}
                        style={{ width: '45%', height: Size.SIZE_42, marginBottom: Spacing.SCALE_10, borderRadius: 8 }}
                    />
                </View>
                <View style={styles.row}>
                    <ShimmerPlaceholder
                        LinearGradient={LinearGradient}
                        style={{ width: '45%', height: Size.SIZE_42, marginBottom: Spacing.SCALE_10, borderRadius: 8 }}
                    />
                    <ShimmerPlaceholder
                        LinearGradient={LinearGradient}
                        style={{ width: '45%', height: Size.SIZE_42, marginBottom: Spacing.SCALE_10, borderRadius: 8 }}
                    />
                </View>
            </>
        );
    };

    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor={colors.white} />
            <KeyboardAwareScrollView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1, backgroundColor: colors.white }}>
                <SafeAreaView>
                    {isEdit || isComeForAddAddress ? (
                        <TouchableOpacity
                            style={[
                                styles.headerContainer,
                                {
                                    justifyContent: 'flex-start',
                                    alignItems: 'flex-start',
                                    marginTop: Platform.OS === 'ios' ? 0 : Spacing.SCALE_30,
                                },
                            ]}
                            onPress={() => {
                                navigation.goBack();
                            }}>
                            <Back width={18} height={18} fill={colors.fadeBlack} />
                        </TouchableOpacity>
                    ) : (
                        <TouchableOpacity
                            style={styles.headerContainer}
                            onPress={() => {
                                logoutUser();
                            }}>
                            <Logout height={24} width={24} />
                        </TouchableOpacity>
                    )}
                </SafeAreaView>
                <ScrollView contentContainerStyle={styles.container}>
                    <Text style={styles.header}>Please enter your Address</Text>
                    <Text style={styles.subHeader}>
                        This address will be used to connect new users to TG Ambassador such as yourselves closest to
                        themselves
                    </Text>
                    {isShowSimmer ? (
                        addressSimmer()
                    ) : (
                        <>
                            <View>
                                <Text style={styles.inputLableStyle}>Enter Address*</Text>
                                <TextInput style={styles.input} value={address} onChangeText={setAddress} />
                            </View>
                            <View style={styles.row}>
                                <View style={styles.column}>
                                    <Text style={styles.inputLableStyle}>Select Country*</Text>
                                    <SelectorInput
                                        type="country"
                                        navigation={navigation}
                                        handleCallBack={handleCallBack}
                                        data={selectedCountry?.name}
                                        placeHolder="Select Country"
                                        searchCallBack={getCountries}
                                    />
                                </View>
                                <View style={styles.column}>
                                    <Text style={styles.inputLableStyle}>Select State</Text>
                                    <SelectorInput
                                        type="state"
                                        navigation={navigation}
                                        handleCallBack={handleCallBack}
                                        data={selectedState?.name}
                                        placeHolder="Select State"
                                        disable={!selectedCountry}
                                        searchCallBack={getStates}
                                    />
                                </View>
                            </View>

                            <View style={styles.row}>
                                <View style={styles.column}>
                                    <Text style={styles.inputLableStyle}>Select City</Text>
                                    <SelectorInput
                                        type="city"
                                        navigation={navigation}
                                        handleCallBack={handleCallBack}
                                        data={selectedCity?.name}
                                        placeHolder="Select City"
                                        disable={!selectedState}
                                        searchCallBack={getAllCities}
                                    />
                                </View>
                                <View style={styles.column}>
                                    <Text style={styles.inputLableStyle}>Enter Postal Code*</Text>
                                    <TextInput
                                        style={[styles.input, { marginBottom: Spacing.SCALE_4 }]}
                                        placeholder="Enter Postal Code"
                                        value={postalCode}
                                        onChangeText={setPostalCode}
                                        maxLength={9}
                                        keyboardType="number-pad"
                                        placeholderTextColor={colors.darkgray}
                                    />
                                    {errors?.postalCode && (
                                        <Text style={styles.errorMsgStyle}>{errors?.postalCode}</Text>
                                    )}
                                </View>
                            </View>
                        </>
                    )}
                </ScrollView>
            </KeyboardAwareScrollView>
                <View style={{ justifyContent: 'flex-end', backgroundColor: colors.white }}>
                    <TouchableOpacity
                        onPress={() => {
                            if (isValid) {
                                onClickContinueHandler();
                            }
                        }}
                        style={[styles.button, isValid ? { backgroundColor: colors.darkteal } : {}]}>
                        {loading ? (
                            <ActivityIndicator color={colors.white} />
                        ) : (
                            <Text style={[styles.buttonText, isValid ? { color: colors.white } : {}]}>Continue</Text>
                        )}
                    </TouchableOpacity>
                </View>
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: Spacing.SCALE_15,
        backgroundColor: colors.white,
        flexGrow: 1,
    },
    headerContainer: {
        backgroundColor: colors.white,
        height: Size.SIZE_30,
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
        paddingHorizontal: Spacing.SCALE_15,
    },
    header: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_20,
        lineHeight: Size.SIZE_22,
        marginBottom: Spacing.SCALE_10,
        textAlign: 'center',
        fontWeight: '400',
        color: colors.lightBlack,
    },
    subHeader: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_14,
        textAlign: 'center',
        fontWeight: '400',
        marginBottom: Spacing.SCALE_20,
        lineHeight: Size.SIZE_20,
        color: colors.fadeBlack,
    },
    inputLableStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        marginBottom: Spacing.SCALE_3,
        lineHeight: Size.SIZE_13,
        color: colors.fadeBlack,
    },
    input: {
        height: Size.SIZE_42,
        borderColor: colors.borderGray,
        borderWidth: 1,
        marginBottom: Spacing.SCALE_20,
        paddingLeft: Spacing.SCALE_10,
        borderRadius: 8,
        backgroundColor: colors.white,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
        color: colors.dark_charcoal,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    column: {
        flex: 1,
        marginHorizontal: 5,
    },
    button: {
        backgroundColor: colors.lightgray,
        paddingVertical: Spacing.SCALE_12,
        borderRadius: 8,
        alignItems: 'center',
        marginVertical: Spacing.SCALE_20,
        width: '90%',
        alignSelf: 'center',
    },
    buttonText: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        textAlign: 'center',
        fontWeight: '500',
        color: colors.darkgray,
    },
    errorMsgStyle: {
        color: colors.errorColor,
        fontFamily: 'Ubuntu-Light',
        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_12,
    },
});

const pickerSelectStyles = StyleSheet.create({
    inputIOS: {
        height: Size.SIZE_42,
        borderColor: colors.borderGray,
        borderWidth: 1,
        marginBottom: Spacing.SCALE_20,
        paddingLeft: Spacing.SCALE_10,
        borderRadius: 8,
        backgroundColor: colors.white,
        color: colors.lightBlack, // Text color for selected value
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        paddingRight: Spacing.SCALE_30,
    },
    inputAndroid: {
        height: Size.SIZE_42,
        borderColor: colors.borderGray,
        borderWidth: 1,
        marginBottom: Spacing.SCALE_20,
        paddingLeft: Spacing.SCALE_10,
        borderRadius: 8,
        backgroundColor: colors.white,
        color: colors.lightBlack, // Text color for selected value
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        paddingRight: Spacing.SCALE_30,
        textAlign: 'left',
        flexWrap: 'wrap',
    },
    inputWrapper: {
        height: Size.SIZE_42,
        borderColor: colors.borderGray,
        borderWidth: 1,
        marginBottom: Spacing.SCALE_20,
        paddingLeft: Spacing.SCALE_10,
        borderRadius: 8,
        backgroundColor: colors.white,
        color: colors.lightBlack, // Text color for selected value
        justifyContent: 'center',
    },

    placeholder: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '400',
        color: colors.darkgray,
    },
    iconContainer: {
        top: 2, // Adjust the position of the dropdown arrow (if present)
        right: 10,
    },
    simmerStyle: {
        width: '100%',
        height: Size.SIZE_10,
        marginBottom: Spacing.SCALE_10,
    },
});

export default AddressForm;
