import React, { useContext, Dispatch, SetStateAction } from 'react';

import { GlobalContext } from '../../../context/contextApi';
import UnMuteAccountPopup from '../../unmuteAccountPopup/view/UnMuteAccountPopup';
import { AuthContext } from '../../../context/AuthContext';
import PendingGameRequestPopup from '../../../components/popup/PendingGameRequestPopup';
import ContactInformVisibilityPopup from '../../feed/ContactInformVisibilityPopup';

const HomeScreenPopups = ({
    isHaveToShowPopup,
    setIsHaveToShowPopup,
}: {
    isHaveToShowPopup: boolean;
    setIsHaveToShowPopup: Dispatch<SetStateAction<boolean>>;
}) => {
    const { state } = useContext(GlobalContext);
    const { user } = useContext(AuthContext);
    const handlePopup = () => {
        switch (state?.homeScreenPopupSteps) {
            case 1:
                return <UnMuteAccountPopup user={user} />;
            case 2:
                return <PendingGameRequestPopup />;
            case 3: {
                return isHaveToShowPopup &&
                    user?.show_visibility_popup &&
                    (user?.show_visibility_popup_time === null ||
                        new Date(user?.show_visibility_popup_time) <= new Date()) && (
                        <ContactInformVisibilityPopup
                            isHaveToShowPopup={isHaveToShowPopup}
                            setIsHaveToShowPopup={setIsHaveToShowPopup}
                        />
                    );
            }
        }
    };
    return handlePopup();
};

export default HomeScreenPopups;

