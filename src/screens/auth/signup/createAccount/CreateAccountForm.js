import React, { useState } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView, Dimensions, Linking, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import auth from '@react-native-firebase/auth';

import Input from '../../../../components/fields/InputNew';
import PhoneNumberInput from '../../../../components/fields/PhoneNumberInputNew';
import { validateEmail, validateUsername } from '../../../../utils/validation';
import { colors } from '../../../../theme/theme';
import { signupUrl } from '../../../../service/EndPoint';
import validatePhone from '../../../../utils/validation/validatePhone';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import GreenButton from '../../../../components/buttons/NewGreenButton';
import ButtonSelectNew from '../../../../components/fields/ButtonSelectNew';
import NewCheckbox from '../../../../components/fields/NewCheckbox';
import { isEmpty } from '../../actions';
import { validateEmailFormat } from '../../../../utils/validation/validateEmpty';
import Config from "react-native-config";
import WebViewModal from '../../../../components/modals/WebViewModal';

import validatePasswordNew from '../../../../utils/validation/validatePasswordNew';

const { width } = Dimensions.get('window')

export default function CreateAccountForm() {
    const [errors, setErrors] = useState({});
    const navigation = useNavigation();
    const [loading, setLoading] = useState();
    const [username, setUsername] = useState();
    const [email, setEmail] = useState();
    const [password, setPassword] = useState();
    const [phoneNumber, setPhoneNumber] = useState();
    const [verificationMethod, setVerificationMethod] = useState('text');

    const [opt_in_email, setOptEmail] = useState(true);
    const [accept_Terms, setAcceptTerms] = useState(false);
    const [webViewVisible, setWebViewVisible] = useState(false);
    const [webViewUrl, setWebViewUrl] = useState('');

    const openWebView = (url) => {
        setWebViewUrl(url);
        setWebViewVisible(true);
    };

    async function createAccount() {
        setLoading(true);
        const isFieldsEmpty = isEmpty({ username, email, password, phoneNumber })
        const phone = `${phoneNumber?.dialCode}${phoneNumber?.unmaskedPhoneNumber.trim()}`

        let emailValid = false
        const emailFormat = await validateEmailFormat(email);
        const passwordValid = await validatePasswordNew(password);

        // if (!password.contain(' ')) {
        //     return true;
        // }

        if (emailFormat) {
            emailValid = await validateEmail(email);
        }
        //validateEmailFormat

        const phoneValid = !Object.keys(isFieldsEmpty).includes('phoneNumber') ? await validatePhone(phone) : false

        const phoneValidLength = phone?.length > 11

        const usernameValid = !Object.keys(isFieldsEmpty).includes('username') ? await validateUsername(username) : false

        //   console.log('passwordValid------------>', passwordValid, usernameValid);


        if (Object.keys(isFieldsEmpty).length > 0 || !emailValid || !usernameValid || !passwordValid || !phoneValid || !emailFormat || !phoneValidLength) {
            setLoading(false);
            setErrors({
                email:
                    !emailFormat ? 'Invalid email'
                        : !emailValid ?
                            'Email already exists. If you have already registered, please log in with your email and password. If you forgot your password, use the link below to reset it. To continue registration, enter another email'
                            : null,
                username:
                    !usernameValid &&
                    'Username is already taken. Please choose another username',
                password:
                    !passwordValid &&
                    'Password at least 8 characters must contain one capital letter, one lowercase letter , one special character & not allow space',
                phoneNumber:
                    !phoneValidLength ?
                        'Invalid mobile number' :
                        !phoneValid ?
                            'Mobile number is already taken. Please choose another mobile number' :
                            null,
                ...isFieldsEmpty,

            });
        }
        else if (!accept_Terms) {
            setLoading(false);
            Alert.alert("", "You must agree to our Terms of Use and policies in order to proceed further");
        }
        else {
            const notificationSettings = {
                email: true,
                text: true,
                push: true,
            };
            const account = {
                username,
                password,
                email,
                opt_in_email,
                phone,
                phone_number_details: phoneNumber,
                verificationMethod,
                accept_Terms,
                notificationSettings
            }

            console.log('request->', account);

            const { loginToken } = await fetch(
                `${signupUrl}`,
                {
                    method: 'POST',
                    body: JSON.stringify(account),
                },
            ).then((data) => data.json())
                .catch(error => {
                    setLoading(false)
                    console.log('error', error.message.includes('Network request failed'));
                    if (error.message.includes('Network request failed'))
                        Alert.alert('No Internet!', 'Please check your internet connection and try again')
                });
            setLoading(false);
            auth().signInWithCustomToken(loginToken);
            navigation.navigate('Verify Account');
        }
    }

    return (
        <KeyboardAwareScrollView
            style={styles.container}>
            <View
                style={{
                    justifyContent: 'space-between',
                }}>
                <Input
                    name='username'
                    placeholder="Username"
                    valueState={[username, setUsername]}
                    errors={errors}
                    setErrors={setErrors} />

                <Input
                    name='email'
                    placeholder="Email"
                    valueState={[email, setEmail]}
                    errors={errors}
                    setErrors={setErrors} />

                <Input
                    name='password'
                    placeholder="Password"
                    valueState={[password, setPassword]}
                    errors={errors}
                    setErrors={setErrors} />

                <PhoneNumberInput
                    name='phoneNumber'
                    valueState={[phoneNumber, setPhoneNumber]}
                    errors={errors}
                    setErrors={setErrors}
                />
            </View>

            <View style={styles.containerVM}>
                <Text
                    style={styles.howTitle}>
                    How would you like to verify your account?
                </Text>
                <View
                    style={styles.verificationMethod}>
                    <ButtonSelectNew
                        name="verificationMethod"
                        options={['text', 'email']}
                        valueState={[verificationMethod, setVerificationMethod]}
                    />
                </View>
            </View>

            <SafeAreaView style={styles.containTerm}>
                <NewCheckbox
                    label="Please send me the monthly site updates including events and benefits"
                    valueState={[opt_in_email, setOptEmail]}
                />
                <NewCheckbox
                    valueState={[accept_Terms, setAcceptTerms]}
                    label="I agree to the Thousand Greens Terms of Use, Privacy Policy, & Copyright Dispute Policy"
                    showpolicy={true}
                    child={
                        <Text style={[{
                            paddingLeft: 10,
                            fontFamily: 'Ubuntu-Regular',
                            color: colors.darkgray,
                        }]}>
                            I agree to the Thousand Greens {' '}
                            <Text
                                style={styles.hyperlinkTxt}
                                onPress={() => openWebView('https://thousandgreens.com/terms')}
                            >Terms of Use,</Text>
                            <Text
                                style={styles.hyperlinkTxt}
                                onPress={() => openWebView('https://thousandgreens.com/privacy')}
                            >{' '}Privacy Policy</Text>
                            <Text style={{ color: colors.darkgray }}>{' '}&{' '}</Text>
                            <Text
                                style={styles.hyperlinkTxt}
                                onPress={() => openWebView('https://thousandgreens.com/copyright')}
                            >Copyright Dispute Policy</Text>
                        </Text>
                    }
                />

                <GreenButton
                    buttonContainer={{ width: '100%' }}
                    label="GET MY VERIFICATION CODE"
                    loading={loading}
                    onPress={createAccount}
                />

                <View style={{ paddingTop: 20, flexDirection: 'row' }}>
                    <Text
                        style={{
                            fontFamily: 'Ubuntu-Regular',
                            marginRight: 10,
                            color: colors.darkgray,
                        }}>
                        Already have an account?
                    </Text>
                    <TouchableOpacity
                        onPress={() => {
                            navigation.navigate('Login');
                        }}>
                        <Text
                            style={{
                                color: colors.darkteal,
                                fontFamily: 'Ubuntu-Medium',
                            }}>
                            Log in
                        </Text>
                    </TouchableOpacity>
                </View>

            </SafeAreaView>

            <WebViewModal
                visible={webViewVisible}
                url={webViewUrl}
                onClose={() => setWebViewVisible(false)}
            />
        </KeyboardAwareScrollView>
    );
}


const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 25,
        marginTop: 30,
        flex: 1,
    },

    hyperlinkTxt: {
        color: colors.darkteal,
    },
    containerVM: { width: '100%', alignItems: 'center', marginVertical: 20 },
    containTerm: { paddingBottom: 15, width: width - 60, alignItems: 'center' },
    verificationMethod: {
        flexDirection: 'row',
        width: '100%',
        marginVertical: 20,
        justifyContent: 'space-between',
    },
    howTitle: {
        marginTop: 30,
        fontFamily: 'Ubuntu-Regular',
    }
})