import React, { useContext, useEffect, useState } from 'react';
import {
    View,
    Text,
    Dimensions,
    Image,
    TouchableOpacity,
    Linking,
    SafeAreaView,
    ScrollView,
    ActivityIndicator,
    Alert,
} from 'react-native';
import { colors } from '../../theme/theme';
import CalendarIcon from '../../assets/images/calendar.svg';
import Moment from 'moment';
import LikeIcon from '../../assets/images/like.svg';
import LikedIcon from '../../assets/images/liked.svg';
import TGHeader from '../../components/layout/TGHeader';
import useClient from '../../hooks/useClient';
import { AuthContext } from '../../context/AuthContext';
import useQuery from '../../hooks/useQuery';
import { BENEFIT_BY_ID } from '../../graphql/queries/benefits';
import PromoCodeModal from '../../components/modals/PromoCodeModal';
import RevalPromoCode from '../../components/layout/benefits/RevalPromoCode';
import DisclaimerPopUp from '../../components/layout/events/DisclaimerPopUp';
import TGText from '../../components/fields/TGText';
import { handleTimeFormat } from '../../components/timeFormatComponent/handleTimeFormat';
import { Spacing, Typography } from '../../utils/responsiveUI';
import { updateBenefitCount } from '../../components/layout/benefits/updateBenifit';
import { BENEFIT_INFO_COUNT_TYPE, REVEAL_BENEFIT_COUNT_TYPE } from '../my-TG-Stream-Chat/constants';
import useThumbnail from '../../hooks/useThumbnail';
import constants from '../../utils/constants/constants';

const { width, height } = Dimensions.get('window');

const UPDATE_BENEFIT = `
mutation updateBenefit($benefit_id: uuid!, $benefit: benefit_set_input!) {
    update_benefit_by_pk(pk_columns: {id: $benefit_id}, _set: $benefit) {
      id
    }
}
`;

export default function BenefitDetails({ navigation, route }) {
    const client = useClient();
    const { user } = useContext(AuthContext);

    const { id } = route.params;

    const { data, error } = useQuery(BENEFIT_BY_ID, { id });

    const [benefit, setBenefit] = useState();
    const [loading, setLoading] = useState(true);

    const [liked, setLiked] = useState();
    const [modal, setModal] = useState();
    const [showPopup, setShowPopup] = useState(false);
    const [requestShowPopup, setRequestShowPopup] = useState(false);
    const benefit_photo = useThumbnail(benefit?.photo, constants.ImageSize[1280])?.thumbnailUrl;

    useEffect(() => {
        if (data) {
            setLoading(false);
            if (data?.benefit_by_pk) setBenefit(data?.benefit_by_pk);
            else
                Alert.alert(
                    '404',
                    'The resource you were trying to find does not exist or has been deleted.',
                    [
                        {
                            text: 'OK',
                            onPress: () => navigation.goBack(),
                        },
                    ],
                    { cancelable: false },
                );
        }
    }, [data]);

    useEffect(() => {
        setLoading(false);
    }, [error]);

    useEffect(() => {
        if (benefit) {
            setLiked(benefit?.views?.includes(user.id));
        }
    }, [benefit]);

    if (!benefit) return null;

    return (
        <>
            <SafeAreaView style={{ flex: 1 }}>
                <TGHeader title="Benefit Details" />
                <ScrollView bounces={false} style={{ margin: 15, borderRadius: 10, backgroundColor: 'white' }}>
                    <TouchableOpacity onPress={() => navigation.navigate('Image Show', { imageUri: benefit?.photo })}>
                        <Image
                            resizeMode="contain"
                            source={{ uri: benefit_photo }}
                            style={{
                                width: width - 30,
                                height: height / 4,
                                borderRadius: 10,
                                zIndex: 20,
                                backgroundColor: 'white',
                            }}
                        />
                    </TouchableOpacity>
                    <View
                        style={{
                            padding: 15,
                            backgroundColor: 'white',
                            paddingTop: 25,
                            borderRadius: 10,
                        }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                            }}>
                            <View
                                style={{
                                    maxWidth: benefit?.dateRange ? width - 50 : null,
                                }}>
                                <Text
                                    style={{
                                        fontFamily: 'Ubuntu-Bold',
                                        fontSize: Typography.FONT_SIZE_15,
                                    }}>
                                    {benefit?.title}
                                </Text>
                                <Text
                                    style={{
                                        fontFamily: 'Ubuntu-Regular',
                                        fontSize: 16,
                                        color: colors.darkgray,
                                        paddingTop: 5,
                                        textTransform: 'uppercase',
                                    }}>
                                    {benefit?.benefit_category?.name}
                                </Text>
                            </View>
                        </View>
                        {benefit?.dateRange && (
                            <View>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        marginTop: Spacing.SCALE_5,
                                    }}>
                                    <CalendarIcon height={20} width={20} />
                                    <Text
                                        style={{
                                            marginLeft: 10,
                                            fontFamily: 'Ubuntu-Regular',
                                            color: colors.darkgray,
                                        }}>
                                        {handleTimeFormat(benefit?.dateRange.from, true) ===
                                        handleTimeFormat(benefit?.dateRange.to, true)
                                            ? handleTimeFormat(benefit?.dateRange.from, true)
                                            : `${handleTimeFormat(benefit?.dateRange.from, true)} - ${handleTimeFormat(
                                                  benefit?.dateRange.to,
                                                  true,
                                              )}`}
                                    </Text>
                                </View>
                            </View>
                        )}
                        <Text
                            style={{
                                paddingTop: 10,
                                paddingBottom: 15,
                                fontFamily: 'Ubuntu-Regular',
                                color: colors.darkgray,
                            }}>
                            {benefit?.description}
                        </Text>
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'flex-end',
                                alignItems: 'center',
                            }}>
                            <TouchableOpacity
                                onPress={() => {
                                    setShowPopup(true);
                                    updateBenefitCount({
                                        userId: user.id,
                                        type: REVEAL_BENEFIT_COUNT_TYPE,
                                        benefitId: id,
                                    });
                                }}>
                                <TGText
                                    style={{
                                        color: colors.darkteal,
                                        fontSize: 14,
                                    }}>
                                    REVEAL BENEFIT
                                </TGText>
                            </TouchableOpacity>
                        </View>
                    </View>
                </ScrollView>
                {modal && <PromoCodeModal code={modal.promo_code} closeModal={() => setModal()} />}
                {loading && (
                    <View
                        style={{
                            paddingVertical: 30,
                            flex: 1,
                            position: 'absolute',
                            justifyContent: 'center',
                            left: 0,
                            right: 0,
                            top: 0,
                            bottom: 0,
                            backgroundColor: 'rgba(0,0,0,0.5)',
                        }}>
                        <ActivityIndicator color="#098089" size="large" />
                    </View>
                )}
            </SafeAreaView>
            <RevalPromoCode
                visible={showPopup}
                promoMesage={benefit?.promo_code}
                onPressClose={() => setShowPopup(false)}
                onPress={() => {
                    setRequestShowPopup(true);
                    setShowPopup(false);
                }}
            />

            <DisclaimerPopUp
                visible={requestShowPopup}
                onPressClose={() => setRequestShowPopup(false)}
                onPress={() => {
                    if (benefit?.sendToEmail && benefit?.email) {
                        Linking.openURL(`mailto:${benefit?.email}`);
                    }

                    if (benefit?.sendToWebsite && benefit?.websiteURL) {
                        const linkURL =
                            benefit?.websiteURL.includes('http://') || benefit?.websiteURL.includes('https://')
                                ? benefit?.websiteURL
                                : `https://${benefit?.websiteURL}`;
                        Linking.openURL(linkURL);
                    }
                    updateBenefitCount({ userId: user.id, type: BENEFIT_INFO_COUNT_TYPE, benefitId: id });
                    setRequestShowPopup(false);
                }}
            />
        </>
    );
}
