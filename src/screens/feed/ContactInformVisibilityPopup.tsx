import React, { useContext, useEffect, useState } from 'react';
import { FlatList, StyleSheet, Text, View } from 'react-native';
import { colors } from '../../theme/theme';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
// @ts-ignore
import Contact<PERSON>ockIcon from '../../assets/images/ContactLockSvg.svg';
// @ts-ignore
import MapIcon from '../../assets/images/TealClubIcon.svg';
import { CONTACT_INFO_BODY, CONTACT_INFO_STRING } from '../../utils/constants/strings';
import { AuthContext } from '../../context/AuthContext';
import ToggleButton from '../../components/buttons/ToggleButton';
import CommonCheckBox from '../../components/checkbox/CommonCheckBox';
import TealButtonNew from '../../components/buttons/TealButtonNew';
import { fetcher } from '../../service/fetcher';
import { UPDATE_CLUB_VISIBLE_TO_OTHER } from '../../service/EndPoint';
import CommonBottomSheet from '../../components/commonBottomSheet/CommonBottomSheet';
import { GlobalContext } from '../../context/contextApi';

// Define interface for a UserClub
interface UserClub {
    club_id: string;
    visibleInNetwork: boolean;
    club: {
        name: string;
    };
}

// Props for RenderItem
interface RenderItemProps {
    item: UserClub;
    userClubs: UserClub[];
    setUserClubs: React.Dispatch<React.SetStateAction<UserClub[]>>;
    handleHasChange: () => void;
}

// Props for ContactInformVisibilityPopup
interface ContactInformVisibilityPopupProps {
    isHaveToShowPopup: boolean;
    setIsHaveToShowPopup: React.Dispatch<React.SetStateAction<boolean>>;
}

const RenderItem: React.FC<RenderItemProps> = ({ item, userClubs, setUserClubs, handleHasChange }) => {
    const handleToggleButton = (selectedClub: UserClub, active: boolean) => {
        handleHasChange();
        setUserClubs(
            userClubs.map((club) =>
                club.club_id === selectedClub.club_id ? { ...club, visibleInNetwork: active } : club,
            ),
        );
    };

    return (
        <>
            <View style={styles.row}>
                <View style={styles.rowContent}>
                    <MapIcon />
                    <Text style={styles.itemTitleStyle}>{item.club.name}</Text>
                </View>
                <View style={styles.toggleWrapper}>
                    <ToggleButton
                        onPress={(active: boolean) => handleToggleButton(item, active)}
                        isToggled={item.visibleInNetwork}
                        disabled={false}
                    />
                </View>
            </View>
            <View style={styles.divider} />
        </>
    );
};

const ContactInformVisibilityPopup: React.FC<ContactInformVisibilityPopupProps> = ({
    isHaveToShowPopup,
    setIsHaveToShowPopup,
}) => {
    const { user, refreshUser } = useContext(AuthContext);
    const { actions } = useContext(GlobalContext);
    const [isCheckBoxChecked, setIsCheckBoxChecked] = useState(false);
    const [userClubs, setUserClubs] = useState<UserClub[]>([]);
    const [hasChange, setHasChange] = useState(false);

    useEffect(() => {
        const clubs = JSON.parse(JSON.stringify(user?.clubs)) as UserClub[];
        setUserClubs(clubs);
    }, [user?.clubs]);

    const handleHasChange = () => setHasChange(true);

    const handleCheckBox = () => {
        handleHasChange();
        setIsCheckBoxChecked((prev) => !prev);
    };

    const handleClubVisible = async () => {
        let clubIds = userClubs?.reduce((acc: any, item: any) => {
            let clubID: any = item.club_id;
            let obj: any = {};
            obj[clubID] = item?.visibleInNetwork;
            acc.push(obj);
            return acc;
        }, []);

        const payload = {
            userId: user?.id,
            clubIds: clubIds,
            showPopupAgain: !isCheckBoxChecked,
        };

        fetcher({
            endpoint: UPDATE_CLUB_VISIBLE_TO_OTHER,
            method: 'POST',
            body: payload,
        }).then(async (response) => {
            if (response?.status) {
                await refreshUser();
                setIsHaveToShowPopup(false);
                actions.setHomeScreenPopupState(4);
            }
        });
    };


    return (
        <CommonBottomSheet showPopup={isHaveToShowPopup}>
            <View style={styles.container}>
                <View style={styles.centerElements}>
                    <ContactLockIcon />
                    <Text style={styles.header}>{CONTACT_INFO_STRING}</Text>
                    <View style={styles.bodyTextWrapper}>
                        <Text style={styles.body}>{CONTACT_INFO_BODY}</Text>
                    </View>
                </View>
                <FlatList
                    data={userClubs}
                    renderItem={({ item }) => (
                        <RenderItem
                            item={item}
                            userClubs={userClubs}
                            setUserClubs={setUserClubs}
                            handleHasChange={handleHasChange}
                        />
                    )}
                    keyExtractor={(item) => item.club_id}
                />
                <View style={styles.checkBoxWrapper}>
                    <CommonCheckBox
                        checked={isCheckBoxChecked}
                        disabled={false}
                        label="Do not show this again"
                        onChecked={handleCheckBox}
                        checkBoxWarperStyle={styles.checkBoxStyle}
                    />
                </View>
                <View style={styles.btnWrapper}>
                    <TealButtonNew
                        text="Dismiss"
                        btnStyle={[styles.btnStyle, { backgroundColor: colors.greyRgba }]}
                        textStyle={styles.dismissTextStyle}
                        onPress={() => {
                            setIsHaveToShowPopup(false);
                            actions.setHomeScreenPopupState(4);
                        }}
                        // @ts-ignore
                        disabledStyle={false}
                        disabled={false}
                        loading={false}
                    />
                    <TealButtonNew
                        // @ts-ignore
                        fontSize={16}
                        onPress={handleClubVisible}
                        btnStyle={[styles.btnStyle, { backgroundColor: hasChange ? colors.tealRgb : colors.dustyGrey }]}
                        text="Done"
                        disabled={!hasChange}
                    />
                </View>
            </View>
        </CommonBottomSheet>
    );
};

export default ContactInformVisibilityPopup;

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.whiteRGB,
        maxHeight: Size.SIZE_640,
        borderTopLeftRadius: Size.SIZE_8,
        borderTopRightRadius: Size.SIZE_8,
        width: '100%',
        position: 'absolute',
        bottom: 0,
        paddingVertical: Spacing.SCALE_24,
    },
    centerElements: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: Spacing.SCALE_18,
    },
    header: {
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_32,
        fontSize: Typography.FONT_SIZE_22,
        fontWeight: '500',
        marginTop: Spacing.SCALE_24,
    },
    body: {
        color: colors.greyRgb,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        marginTop: Spacing.SCALE_12,
        marginBottom: Spacing.SCALE_24,
        textAlign: 'center',
    },
    bodyTextWrapper: {
        width: Size.SIZE_300,
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.SCALE_18,
    },
    rowContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    itemTitleStyle: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Medium',
        color: colors.dark_charcoal,
        marginLeft: Spacing.SCALE_8,
        width: Size.SIZE_250,
    },
    divider: {
        backgroundColor: colors.greyRgba,
        height: 1,
        marginTop: Spacing.SCALE_12,
        width: '100%',
        alignSelf: 'center',
    },
    checkBoxWrapper: {
        alignSelf: 'center',
        marginTop: Spacing.SCALE_24,
    },
    checkBoxStyle: {
        height: Size.SIZE_16,
        width: Size.SIZE_16,
        borderColor: colors.tealRgb,
        borderRadius: 2,
        borderWidth: 1,
    },
    btnWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: Spacing.SCALE_20,
        backgroundColor: colors.whiteRGB,
        paddingVertical: Spacing.SCALE_20,
    },
    btnStyle: {
        borderRadius: Size.SIZE_8,
        alignSelf: 'center',
        width: '45%',
    },
    dismissTextStyle: {
        fontSize: 16,
        color: '#333333',
        fontFamily: 'Ubuntu-medium',
    },
    toggleWrapper: {
        marginRight: 10,
    },
});
