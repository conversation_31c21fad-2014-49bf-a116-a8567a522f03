import React, { useContext } from 'react';
import { GlobalContext } from '../../../context/contextApi';
import StreamEmptyScreen from '../view/StreamEmptyScreen';
import EmptyStateScreen from '../TGChatComponent/EmptyStateScreen';
import { GROUP } from '../../../utils/constants/strings';

const EmptyScreen = (props) => {
    const { searchData, triggerSearchString, activeTab } = props;
    const { state } = useContext(GlobalContext);
    if (searchData === '' && triggerSearchString === '') {
        if (state.archiveState) {
            return <StreamEmptyScreen screenText={`You haven't archived any chat yet`} screen={''} />;
        } else if (activeTab === GROUP) {
            return <StreamEmptyScreen screenText={`You’re not part of any TG groups yet!`} screen={`ChannelList`} />;
        } else {
            return <StreamEmptyScreen screenText={`You don't have any chats yet`} screen={`ChannelList`} />;
        }
    } else if (searchData !== '') {
        return (
            <EmptyStateScreen
                title="No Results Found"
                description=" No result found. Please try some other term"
                scrollEnabled={false}
            />
        );
    }
    return null;
};

export default EmptyScreen;
