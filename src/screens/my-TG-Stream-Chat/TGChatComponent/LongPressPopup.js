import React, { useContext, useMemo, useState } from 'react';
import { Pressable, StyleSheet, View, Text } from 'react-native';

import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import {
    ARCHIVE_TEXT,
    LAVE_GROUP_POPUP_CONTENT,
    MY_TG_GROUP,
    ONE_TO_ONE,
    SYSTEM_PRIVATE_NETWORK,
    SYSTEM_THOUSAND_GREENS_PUBLIC,
    UNARCHIVE_TEXT,
} from '../client';
import { muteChannel, nuMuteChannel } from '../commonAction/muteUnMuteAction';
import { StreamChatContext } from '../../../context/StreamChatContext';
import { handlePopupTitle } from '../commonAction/mutePopupAction';
import { removeParticipants } from '../action';
import { AuthContext } from '../../../context/AuthContext';
import { useNavigation } from '@react-navigation/native';
import commonAlert from './CommonAlert';
import { handleArchive, handleUnArchive } from '../commonAction/archiveAction';
import { handleArchivePopupHeader } from '../commonAction/handleArchivePopupHeader';
import { GlobalContext } from '../../../context/contextApi';
import { deleteGroup } from '../../myTgGroup/MyTgGroupInfo/action/deleteGroup';
import { DELETE_CHAT, DELETE_CHAT_STRING, DELETE_GROUP, DELETE_GROUP_STRING } from '../../../utils/constants/strings';

const LongPressPopup = ({ route }) => {
    const { callfilter } = route?.params;
    const { addMutedChannel, removeMutedChannel, mutedChannels, channel, client } = useContext(StreamChatContext);
    const { actions, state } = useContext(GlobalContext);
    const { selectedChannel } = state;
    const { user } = useContext(AuthContext);
    const [screenLoading, setScreenLoading] = useState(false);
    const navigation = useNavigation();

    const { type, bottomShetButtonText, isMute, hidden, archivePopupHeader } = useMemo(() => {
        let isMute = mutedChannels?.includes(selectedChannel?.id);
        let type = handlePopupTitle(selectedChannel, isMute);
        let bottomShetButtonText = mutedChannels?.includes(selectedChannel?.id) ? 'Unmute' : 'Mute';
        const { data: { hidden } = {} } = selectedChannel || {};
        const archivePopupHeader = handleArchivePopupHeader(selectedChannel, hidden);
        return { type, bottomShetButtonText, isMute, hidden, archivePopupHeader };
    }, [selectedChannel]);
    console.log('archivePopupHeader', archivePopupHeader);

    const onChannelDeletion = async () => {
        actions?.setDeletePopupLoader(true);
        if (selectedChannel?.type === ONE_TO_ONE) {
            await selectedChannel?.hide(user?.id, true);
            await selectedChannel?.show(user?.id);
            actions?.setDeleteChannelPopupState(false);
            actions?.setDeletePopupLoader(false);
        } else if (selectedChannel?.type === MY_TG_GROUP) {
            //Hide the channel from FE and it will eventually get deleted from the backend
            await selectedChannel?.hide(user?.id, true);

            const response = await deleteGroup({ userId: user?.id, groupId: selectedChannel?.data?.id }, () => {});
            if (response && response?.status) {
                actions?.setDeleteChannelPopupState(false);
            } else {
                //For any worse case, if the channel is not gets deleted by BE, then show that channel again on FE.
                await selectedChannel?.show(user?.id);
            }
        }
    };

    const handleDeleteConfirmationScreen = () => {
        navigation.push('DeleteChannelConfirmationPopup', {
            handleYesButton: onChannelDeletion,
            popupSubText: selectedChannel?.type === MY_TG_GROUP ? DELETE_GROUP_STRING : DELETE_CHAT_STRING,
            popupHeader: selectedChannel?.type === MY_TG_GROUP ? DELETE_GROUP : DELETE_CHAT,
        });
    };

    const handleMute = () => {
        if (mutedChannels?.includes(selectedChannel?.id)) {
            callfilter();
            nuMuteChannel(selectedChannel, user?.id);
            removeMutedChannel(selectedChannel?.id);
        } else {
            callfilter();
            muteChannel(selectedChannel, user?.id);
            addMutedChannel(selectedChannel?.id);
        }
    };

    const handleOnPress = () => {
        navigation.navigate('DeleteChannelConfirmationPopup', {
            handleYesButton: async () => {
                handleMute();
            },
            popupHeader: 'Mute Channel',
            popupSubText: type,
            firstBtnLabel: 'No',
            secondBtnLabel: 'Yes',
        });
    };

    const handleLeaveGroup = () => {
        navigation.navigate('DeleteChannelConfirmationPopup', {
            handleYesButton: async () => {
                removeParticipants(
                    user?.id,
                    selectedChannel?.id,
                    user?.id,
                    setScreenLoading,
                    navigation,
                    true,
                    actions,
                    selectedChannel?.data?.type,
                );
            },
            popupHeader: 'Leave Channel',
            popupSubText: LAVE_GROUP_POPUP_CONTENT,
            firstBtnLabel: 'Cancel',
            secondBtnLabel: 'Leave',
        });
    };
    const handleLeaveOptionPress = () => {
        navigation.pop(1);
        setTimeout(() => {
            handleLeaveGroup();
        }, 500);
    };

    const archive = async () => {
        handleArchive(selectedChannel, user?.id);
    };
    const unArchive = async () => {
        handleUnArchive(selectedChannel, user?.id);
    };

    const handleArchiveOnPress = () => {
        navigation.pop(1);
        navigation.navigate('DeleteChannelConfirmationPopup', {
            handleYesButton: async () => {
                if (hidden) {
                    unArchive();
                } else {
                    archive();
                }
            },
            popupHeader: 'Archive Channel',
            popupSubText: archivePopupHeader,
            firstBtnLabel: 'Cancel',
            secondBtnLabel: hidden ? 'Unarchive' : 'Archive',
        });
    };

    const handleLeaveGroupUI = () => {
        if (
            !(selectedChannel?.type === SYSTEM_THOUSAND_GREENS_PUBLIC) &&
            selectedChannel?.type !== SYSTEM_PRIVATE_NETWORK
        ) {
            if (
                selectedChannel?.type === ONE_TO_ONE ||
                (selectedChannel?.type === MY_TG_GROUP && selectedChannel?.data?.creator_id === user?.id)
            ) {
                return (
                    <>
                        <View style={styles.dividerStyle} />
                        <Pressable
                            style={styles.popupContent}
                            onPress={() => {
                                navigation.pop(1);
                                setTimeout(() => {
                                    handleDeleteConfirmationScreen();
                                }, 500);
                            }}>
                            <Text style={styles.popupOptionStyle}>Delete</Text>
                        </Pressable>
                    </>
                );
            } else {
                return (
                    <>
                        <View style={styles.dividerStyle} />
                        <Pressable style={styles.popupContent} onPress={handleLeaveOptionPress}>
                            <Text style={styles.popupOptionStyle}>Leave</Text>
                        </Pressable>
                    </>
                );
            }
        } else return null;
    };

    return (
        <View style={styles.modalStyle}>
            <View style={styles.container}>
                <Pressable style={styles.blankScreenWrapper} />
                <View style={styles.bodyStyle}>
                    <View style={styles.popupWrapper}>
                        <Pressable style={styles.popupContent} onPress={handleArchiveOnPress}>
                            <Text style={styles.popupOptionStyle}>{hidden ? 'Unarchive' : 'Archive'}</Text>
                        </Pressable>
                        {!(selectedChannel?.type === SYSTEM_THOUSAND_GREENS_PUBLIC) && (
                            <>
                                <View style={styles.dividerStyle} />
                                <Pressable
                                    style={styles.popupContent}
                                    onPress={() => {
                                        navigation.pop(1);
                                        setTimeout(() => {
                                            handleOnPress();
                                        }, 500);
                                    }}>
                                    <Text style={styles.popupOptionStyle}>{bottomShetButtonText}</Text>
                                </Pressable>
                            </>
                        )}
                        {handleLeaveGroupUI()}
                    </View>
                    <Pressable style={styles.popupWrapper1} onPress={() => navigation.pop(1)}>
                        <Text style={styles.cancelBtnStyle}>Cancel</Text>
                    </Pressable>
                </View>
            </View>
        </View>
    );
};
export default LongPressPopup;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    popupWrapper: {
        width: Size.SIZE_340,
        position: 'absolute',
        bottom: Spacing.SCALE_85,
        backgroundColor: 'rgba(248, 248, 248, 1)',
        borderRadius: Size.SIZE_18,
        marginHorizontal: Spacing.SCALE_6,
        alignItems: 'center',
    },
    popupWrapper1: {
        width: Size.SIZE_340,
        minHeight: Size.SIZE_54,
        position: 'absolute',
        bottom: Spacing.SCALE_24,
        backgroundColor: '#FFFFFF',
        borderRadius: Size.SIZE_14,
        marginHorizontal: 7,
        justifyContent: 'center',
        alignItems: 'center',
    },
    listHeaderText1: {
        fontSize: Typography.FONT_SIZE_11,
        color: 'white',
        fontFamily: 'Ubuntu-Medium',
    },
    logo: {
        width: Size.SIZE_18,
        height: Size.SIZE_18,
        tintColor: 'white',
    },
    popupHeaderText: {
        color: 'rgba(51, 51, 51, 1)',
        fontSize: Typography.FONT_SIZE_24,
        fontWeight: '500',
        lineHeight: Size.SIZE_27,
        paddingHorizontal: Spacing.SCALE_18,
        fontFamily: 'Ubuntu-Medium',
    },
    headerWrapper: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingVertical: Spacing.SCALE_20,
    },
    crossIconWrapper: {
        paddingHorizontal: 20,
        paddingTop: 5,
    },
    popupWrapperData: {
        flexDirection: 'row',
        paddingHorizontal: Spacing.SCALE_20,
        paddingVertical: 20,
    },
    clubIconWrapper: {
        width: Size.SIZE_45,
        height: Size.SIZE_45,
        backgroundColor: '#F2F2F2',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: Size.SIZE_4,
    },
    popupTextWrapper: {
        marginLeft: Spacing.SCALE_13,
        marginTop: Spacing.SCALE_10,
    },
    popupTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
    },
    cancelBtnStyle: {
        color: 'rgba(0, 122, 255, 1)',
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '600',
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
    },
    popupContent: {
        height: Size.SIZE_40,
        justifyContent: 'center',
    },
    popupOptionStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        color: 'rgba(0, 122, 255, 1)',
        fontFamily: 'Ubuntu-Medium',
    },
    modalStyle: {
        flex: 1,
        // paddingHorizontal: 0,
        // marginHorizontal: 0,
        // paddingVertical: 0,
        // marginVertical: 0,
        // zIndex: 1000,
        // elevation: 1000, // For Android
    },
    blankScreenWrapper: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    bodyStyle: {
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        width: '100%',
        minHeight: '100%',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dividerStyle: {
        backgroundColor: 'rgba(196, 196, 196, 1)',
        height: 1,
        width: '95%',
    },
});
