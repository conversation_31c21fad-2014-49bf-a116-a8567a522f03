import React, { useContext } from 'react';
import { TouchableOpacity } from 'react-native';
import { RootPath, RootSvg, useMessageInputContext } from 'stream-chat-react-native';

//Common Methods and Contexts imports
import { updateMessageKey } from '../../requests/stream-chat/action/updateMessageKey';
import { StreamChatContext } from '../../../context/StreamChatContext';

const StreamButton = ({ graphQlClient, hasMessages, createdById, user, requestId, rollbar }) => {
    const { sendMessage, text, imageUploads, fileUploads } = useMessageInputContext();
    const { channel } = useContext(StreamChatContext);
    const isDisabled = !text && !imageUploads?.length && !fileUploads?.length;

    // Unified function to send attachments individually
    const sendAttachmentsIndividually = async (attachments, attachmentType) => {
        try {
            const promises = attachments.map(async (attachment, index) => {
                const messageData = {
                    text: index === 0 ? text : '',
                    attachments: [],
                };

                if (attachmentType === 'image') {
                    messageData.attachments.push({
                        type: 'image',
                        image_url: attachment.url,
                        thumb_url: attachment.url,
                        original_width: attachment.width,
                        original_height: attachment.height,
                        originalImage: attachment.file,
                    });
                } else if (attachmentType === 'file') {
                    if (attachment.type === 'video') {
                        messageData.attachments.push({
                            type: 'video',
                            asset_url: attachment.url,
                            thumb_url: attachment.thumb_url,
                            title: attachment.file.name,
                            duration: attachment.duration,
                            file_size: attachment.file.size,
                            mime_type: attachment.file.mimeType,
                            originalFile: attachment.file,
                        });
                    } else {
                        messageData.attachments.push({
                            type: 'image',
                            image_url: attachment.url,
                            thumb_url: attachment.url,
                            original_width: attachment.width,
                            original_height: attachment.height,
                            originalImage: attachment.file,
                        });
                    }
                }

                return await channel.sendMessage(messageData);
            });

            await Promise.all(promises);
        } catch (error) {
            console.error(`Error sending ${attachmentType} attachments:`, error);
            // Fallback to default sendMessage if individual sending fails
            sendMessage();
        }
    };

    // Optimized message sending function that handles all conditions
    const handleSendMessage = async () => {
        try {
            // Update message key first
            updateMessageKey({ graphQlClient, hasMessages, createdById, user, requestId, rollbar });

            const hasImages = imageUploads?.length > 0;
            const hasFiles = fileUploads?.length > 0;
            const totalAttachments = (imageUploads?.length || 0) + (fileUploads?.length || 0);

            // Case 1: No attachments, just text
            if (!hasImages && !hasFiles) {
                sendMessage();
                return;
            }

            // Case 2: Too many attachments (use default sendMessage)
            if (totalAttachments > 3) {
                sendMessage();
                return;
            }

            // Case 3: Only images
            if (hasImages && !hasFiles) {
                await sendAttachmentsIndividually(imageUploads, 'image');
                return;
            }

            // Case 4: Only files
            if (!hasImages && hasFiles) {
                await sendAttachmentsIndividually(fileUploads, 'file');
                return;
            }

            // Case 5: Both images and files (send them sequentially)
            if (hasImages && hasFiles) {
                await sendAttachmentsIndividually(imageUploads, 'image');
                await sendAttachmentsIndividually(fileUploads, 'file');
                return;
            }
        } catch (error) {
            console.error('Error in handleSendMessage:', error);
            // Fallback to default sendMessage
            sendMessage();
        }
    };

    return (
        <TouchableOpacity disabled={isDisabled} onPress={handleSendMessage}>
            <RootSvg height={21} width={42} viewBox="0 0 42 21">
                <RootPath
                    d="M1.101 21.757 23.8 12.028 1.101 2.3l.011 7.912 13.623 1.816-13.623 1.817-.011 7.912z"
                    pathFill={isDisabled ? 'grey' : 'rgb(9, 128, 137)'}
                    pathOpacity={1}
                />
            </RootSvg>
        </TouchableOpacity>
    );
};

export default StreamButton;
