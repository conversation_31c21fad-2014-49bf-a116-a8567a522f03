import React, { useContext, useMemo } from 'react';
import { StyleSheet, Text } from 'react-native';

import { AuthContext } from '../../../context/AuthContext';
import { colors } from '../../../theme/theme';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { getOneToOneChannelName } from '../action';
import { CHANNEL_TITLE_LIMIT, ONE_TO_ONE } from '../client';

const CustomPreviewTitle = ({ data, state }) => {
    const { channel } = data;
    const { user } = useContext(AuthContext);

    const { updatedName, membersCount, type } = useMemo(() => {
        const name = getOneToOneChannelName(channel, user, state?.allFriendsId);
        const updatedName =
            name?.length > CHANNEL_TITLE_LIMIT
                ? `${name.substring(0, CHANNEL_TITLE_LIMIT)}...`
                : name;
        const { state: { members } = {}, data: { type } = {} } = channel || {};
        let membersCount = Object.keys(members).length;
        return { updatedName, membersCount, type };
    }, [data]);

    if (membersCount === 1 && type === ONE_TO_ONE)
        return (
            <Text style={styles.deletedUserTextStyle}>{'Deleted User'}</Text>
        );
    else return <Text style={styles.container}>{updatedName}</Text>;
};

export default CustomPreviewTitle;

const styles = StyleSheet.create({
    container: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '500',
        color: 'rgba(0, 0, 0, 1)',
        fontFamily: 'Ubuntu-Medium',
        paddingTop: Spacing.SCALE_10
    },
    deletedUserTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '500',
        color: colors.lightShadeGray,
        fontFamily: 'Ubuntu-Medium',
    },
});
