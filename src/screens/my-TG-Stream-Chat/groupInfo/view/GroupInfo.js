import React, { useContext, useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, Alert, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';

import StreamHeader from '../../../../components/layout/StreamHeader';
import { AuthContext } from '../../../../context/AuthContext';
import GroupInfoIconComponent from '../../TGChatComponent/GroupInfoIconComponent';
import ComponentFlatList from './ComponentFlatList';
import ComponentHeader from './ComponentHeader';
import { updateGroupName, updateGroupProfile } from '../action/editGroupInfoApi';
import GroupInfoLongPressPopup from '../../TGChatComponent/GroupInfoLongPressPopup';
import styles from '../style/GroupInfoStyle';
import { StreamChatContext } from '../../../../context/StreamChatContext';
import AddParticipants from '../../TGChatComponent/AddParticipantsPopup';
import { colors } from '../../../../theme/theme';
import getAllChannelMembers from '../action/getAllChannelMembers';
import sortByLastName from '../action/sortingGroupMembers';
import { GlobalContext } from '../../../../context/contextApi';
import {
    ADMIN_CREATED_GROUP,
    ARCHIVE_TEXT,
    LAVE_GROUP_POPUP_CONTENT,
    MUTE_GROUP_POPUP_CONTENT,
    MY_TG_GROUP,
    UNMUTE_GROUP_POPUP_CONTENT,
} from '../../client';
import { removeParticipants } from '../../action';
import { muteChannel, nuMuteChannel } from '../../commonAction/muteUnMuteAction';
import commonAlert from '../../TGChatComponent/CommonAlert';
import { handleArchive, handleUnArchive } from '../../commonAction/archiveAction';
import FooterComponent from './FooterComponent';
import TGLoader from '../../../../components/layout/TGLoader';
import { handleArchivePopupHeader } from '../../commonAction/handleArchivePopupHeader';
import { updateGroup } from '../../../myTgGroup/createGroup/action/updateGroup';
import getMyTGChannelCapacity from '../action/getMyTGChannelCapcity';
import getTags from '../action/getTags';
import ShowTagUi from '../../../../components/myTgGroup/ShowTagUi';
import MembersByClub from '../../../myTgGroup/MyTgGroupInfo/view/MemberByClub';
import { getGroupMembers } from '../../../myTgGroup/MyTgGroupInfo/action/getGroupMembers';
import config from '../../../../config';
import { getStreamChannel } from '../../../myTgGroup/helperFunctions/querryChannel';

const GroupInfo = () => {
    const { user } = useContext(AuthContext);
    const { channel, setChannel, addMutedChannel, removeMutedChannel, mutedChannels, client, updateChannel } =
        useContext(StreamChatContext);
    const { state, actions } = useContext(GlobalContext);
    const [photo, setPhoto] = useState('');
    const [loading, setLoading] = useState(false);
    const [screenLoading, setScreenLoading] = useState(false);
    const [allMembersLoading, setAllMembersLoading] = useState(false);
    const [currentUser, setCurrentUser] = useState();
    const [totalMembers, setTotalMembers] = useState([]);
    const [isEditGroupName, setIsEditGroupName] = useState(false);
    const [name, setName] = useState('');
    const [error, setError] = useState('');
    const [longPressState, setLongPressState] = useState(false);
    const [selectedUser, setSelectedUser] = useState();
    const [showForwardPopup, setShowForwardPopup] = useState(false);
    const [isMute, setIsMute] = useState(false);
    const [archiveStates, setArchiveStates] = useState(false);
    const [groupCapacity, setGroupCapacity] = useState(0);
    const [tags, setTags] = useState([]);
    const [toggleMemberList, setToggleMemberList] = useState(true);
    const [loadingGroup, setLoadingGroup] = useState(false);

    const navigation = useNavigation();
    const { updatedChannel } = state;
    let members;

    const getGroupChannel = async () => {
        setLoadingGroup(true);
        await getStreamChannel(client, user?.id, updatedChannel?.data?.id, setChannel, actions);
        setLoadingGroup(false);
    };
    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', async () => {
            // Code to run when screen is focused
            try {
                getGroupChannel();
            } catch (error) {
                console.log('error ->', error);
            }
        });

        // Clean up the listener when the component unmounts
        return unsubscribe;
    }, [navigation]);

    useEffect(() => {
        actions?.setCurrentScreenName(null);
        actions?.updatedChannelAction(channel);
        if (mutedChannels?.includes(channel?.id)) {
            setIsMute(true);
        } else {
            setIsMute(false);
        }
    }, [mutedChannels]);

    const { hidden } = useMemo(() => {
        const { data: { hidden } = {} } = channel || {};
        return { hidden };
    }, [channel, channel?.data]);

    const getAllMembers = async () => {
        members = await getAllChannelMembers(channel, {});
        members = members.sort(sortByLastName);
        actions?.channelMembersAction(members);
        setTotalMembers(members);
        actions.setAllMemberScreenLoader(false);
        return members;
    };

    useEffect(() => {
        getAllMembers();
    }, [channel?.state?.members]);

    const { data, mutePopupText, archivePopupHeader } = useMemo(() => {
        const { data, state } = channel || {};
        setName(data?.name);
        setPhoto(data?.image);
        let mutePopupText = isMute ? UNMUTE_GROUP_POPUP_CONTENT : MUTE_GROUP_POPUP_CONTENT;
        const archivePopupHeader = handleArchivePopupHeader(channel, channel?.data?.hidden);
        return { data, state, mutePopupText, archivePopupHeader };
    }, [channel?.data?.name, channel?.data?.image, channel?.data, channel?.state?.members, isMute]);

    useEffect(() => {
        setCurrentUser(Object.values(totalMembers).find((member) => member?.user_id === user?.id));
    }, [totalMembers]);

    useEffect(() => {
        const channelUpdatedEventListener = async (event) => {
            if (channel?.data?.name !== event?.channel?.name || channel?.data?.image !== event?.channel?.image) {
                await channel?.watch();
                setChannel(channel);
            }
        };

        const channelUpdatedListener = channel?.on && channel?.on('channel.updated', channelUpdatedEventListener);

        /**
         * The below event listener is for listening to the changes when members are updated in a channel
         * We are using this for updating the members list in the context
         */
        const memberUpdatedListener =
            channel?.on &&
            channel?.on('member.updated', async (event) => {
                const members = await getAllMembers();
            });
        const memberRemovedListener =
            channel?.on &&
            channel?.on('member.removed', async (event) => {
                const members = await getAllMembers();
                if (event?.member?.user?.id === user?.id) {
                    navigation.navigate(config.routes.BOTTOM_TAB_NAVIGATION, {
                        screen: config.routes.TG_CHAT,
                    });
                }
            });
        const channelVissibleEventListner = client.on('channel.visible', async (event) => {
            await channel?.watch();
            updateChannel(channel);
            setChannel(channel);
            actions?.updatedChannelAction(channel);
        });
        const eventListner = client.on('channel.hidden', async (event) => {
            await channel?.watch();
            updateChannel(channel);
            setChannel(channel);
            actions?.updatedChannelAction(channel);
        });

        return () => {
            eventListner?.unsubscribe();
            channelVissibleEventListner?.unsubscribe();
            channelUpdatedListener?.unsubscribe();
            memberUpdatedListener?.unsubscribe();
            memberRemovedListener?.unsubscribe();
        };
    }, [channel, client, channel?.data?.hidden, totalMembers]);

    const updateGroupProfileIcon = (photoURL) => {
        if (data?.type === MY_TG_GROUP) {
            updateGroup(
                {
                    userId: user?.id,
                    groupId: data?.id,
                    groupImage: photoURL,
                    groupName: data?.name,
                    description: data?.description,
                },
                setLoading,
            );
        } else {
            updateGroupProfile(user?.id, data?.id, photoURL, setLoading);
        }
    };

    const handleCheckIconPress = () => {
        if (name !== '') {
            if (data?.type === MY_TG_GROUP) {
                updateGroup(
                    {
                        userId: user?.id,
                        groupId: data?.id,
                        groupName: name.trimEnd(),
                        description: data?.description,
                    },
                    setScreenLoading,
                    undefined,
                    undefined,
                    setIsEditGroupName,
                );
            } else {
                setScreenLoading(!screenLoading);
                updateGroupName(user?.id, data?.id, name, setLoading, setIsEditGroupName, setScreenLoading);
            }
        } else {
            setError('Enter a valid Group name.');
        }
    };

    const handleMute = () => {
        if (mutedChannels?.includes(channel?.id)) {
            nuMuteChannel(channel, user?.id);
            removeMutedChannel(channel?.id);
        } else {
            muteChannel(channel, user?.id);
            addMutedChannel(channel?.id);
        }
    };

    const handleMuteGroup = () => {
        navigation.navigate('DeleteChannelConfirmationPopup', {
            handleYesButton: async () => {
                handleMute();
            },
            popupSubText: mutePopupText,
            firstBtnLabel: 'No',
            secondBtnLabel: 'Yes',
        });
    };

    const handleLeaveGroup = () => {
        navigation.navigate('DeleteChannelConfirmationPopup', {
            handleYesButton: async () => {
                removeParticipants(
                    user?.id,
                    channel?.id,
                    user?.id,
                    setScreenLoading,
                    navigation,
                    true,
                    actions,
                    channel?.data?.type,
                );
            },
            popupSubText: LAVE_GROUP_POPUP_CONTENT,
            firstBtnLabel: 'Cancel',
            secondBtnLabel: 'Leave',
        });
    };

    const archive = async () => {
        handleArchive(channel, user?.id);
    };
    const unArchive = async () => {
        handleUnArchive(channel, user?.id);
    };

    const handleArchiveOnPress = () => {
        const showPopup = (popupSubText, callBacks) => {
            navigation.navigate('DeleteChannelConfirmationPopup', {
                handleYesButton: async () => {
                    callBacks();
                },
                popupSubText: popupSubText,
                firstBtnLabel: 'No',
                secondBtnLabel: 'Yes',
            });
        };
        setLongPressState(false);
        if (hidden) {
            setTimeout(() => {
                showPopup(archivePopupHeader, unArchive);
                setArchiveStates(false);
            }, 500);
        } else {
            setTimeout(() => {
                showPopup(archivePopupHeader, archive);
                setArchiveStates(true);
            }, 500);
        }
    };

    // If channel type is my_tg_group then
    useEffect(() => {
        //Api calling to fetch tags
        fetchTags();

        const getCapacity = async () => {
            const response = await getMyTGChannelCapacity({
                groupId: channel?.id,
            });
            if (response.status) {
                setGroupCapacity(response?.capacityLeft);
            }
        };
        if (channel?.type === MY_TG_GROUP) {
            getCapacity();
        }
    }, [channel?.id, totalMembers]);

    //Fetch tags related to each groups
    const fetchTags = async () => {
        const tagResponse = await getTags({
            userId: user?.id,
            groupId: channel?.id,
        });
        if (tagResponse.status) {
            setTags(tagResponse?.tags);
        }
    };

    useEffect(() => {
        if (channel?.type === MY_TG_GROUP) getMyTGGroupMember();
    }, []);

    const getMyTGGroupMember = () => {
        setAllMembersLoading(true);
        getGroupMembers({ userId: user?.id, groupId: channel?.id }, setAllMembersLoading, actions);
    };

    const { clubMembers } = useMemo(() => {
        const { clubMembers } = state?.myTgGroupMembers;
        return { clubMembers };
    }, [state?.myTgGroupMembers]);

    return (
        <>
            <View style={styles.container}>
                <StreamHeader screenName={`Group Info`} />
                {loadingGroup ? (
                    <TGLoader loading={loadingGroup} loaderColor={colors.darkteal} />
                ) : (
                    <ScrollView style={styles.box}>
                        <GroupInfoIconComponent
                            setLoading={setLoading}
                            setPhoto={setPhoto}
                            photo={photo}
                            loading={loading}
                            data={data}
                            updateGroupProfileIcon={updateGroupProfileIcon}
                            currentUser={currentUser}
                        />
                        <ComponentHeader
                            isEditGroupName={isEditGroupName}
                            setIsEditGroupName={setIsEditGroupName}
                            name={name}
                            setName={setName}
                            currentUser={currentUser}
                            data={data}
                            setError={setError}
                            error={error}
                            handleCheckIconPress={handleCheckIconPress}
                            showForwardPopup={showForwardPopup}
                            setShowForwardPopup={setShowForwardPopup}
                            totalMembers={totalMembers}
                            groupCapacity={groupCapacity}
                        />
                        {channel?.data?.description &&
                            (channel?.data?.type === ADMIN_CREATED_GROUP || channel?.data?.type === MY_TG_GROUP) && (
                                <View style={styles.descriptionWrapper}>
                                    <Text style={styles.descriptionText}>Description</Text>
                                    <Text style={styles.description}>{channel?.data?.description}</Text>
                                </View>
                            )}
                        {/* Show tags UI */}
                        {tags?.length !== 0 && <ShowTagUi tags={tags} />}

                        {channel?.type === MY_TG_GROUP && (
                            <View style={styles.memberTextbox}>
                                <View style={styles.toggleContainer}>
                                    <View>
                                        <Text style={styles.memberText}>{state?.channelMembers?.length} Members</Text>
                                    </View>
                                    <View>
                                        <View style={styles.toggleBtnContainer}>
                                            <TouchableOpacity
                                                style={[styles.singleBtn, toggleMemberList && styles.activeBtn]}
                                                onPress={() => setToggleMemberList(true)}>
                                                <Text
                                                    style={[
                                                        styles.btnTextStyle,
                                                        toggleMemberList && styles.activeBtntext,
                                                    ]}>
                                                    Members
                                                </Text>
                                            </TouchableOpacity>
                                            <TouchableOpacity
                                                style={[styles.singleBtn, !toggleMemberList && styles.activeBtn]}
                                                onPress={() => setToggleMemberList(false)}>
                                                <Text
                                                    style={[
                                                        styles.btnTextStyle,
                                                        !toggleMemberList && styles.activeBtntext,
                                                    ]}>
                                                    Clubs
                                                </Text>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </View>
                                <Text style={styles.memberSecondText}>
                                    You can check out members organized by their respective clubs in the "Clubs" tab.
                                </Text>
                            </View>
                        )}

                        {toggleMemberList ? (
                            <ComponentFlatList
                                popupState={[longPressState, setLongPressState]}
                                setSelectedUser={setSelectedUser}
                                setAllMembersLoading={setAllMembersLoading}
                                allMembersLoading={allMembersLoading}
                            />
                        ) : (
                            <MembersByClub getMemberLoading={allMembersLoading} totalClubs={clubMembers} />
                        )}
                        <FooterComponent
                            isMute={isMute}
                            handleLeaveGroup={handleLeaveGroup}
                            handleArchiveOnPress={handleArchiveOnPress}
                            handleMuteGroup={handleMuteGroup}
                            archiveStates={archiveStates}
                            hidden={hidden}
                            currentUser={currentUser}
                        />
                    </ScrollView>
                )}
            </View>
            <TGLoader loading={screenLoading} loaderColor={colors.darkteal} />
            {longPressState && (
                <GroupInfoLongPressPopup
                    popupState={[longPressState, setLongPressState]}
                    data={data}
                    selectedUser={selectedUser}
                    setScreenLoading={setScreenLoading}
                />
            )}
            {showForwardPopup && (
                <AddParticipants
                    showForwardPopup={showForwardPopup}
                    setShowForwardPopup={setShowForwardPopup}
                    data={data}
                    totalMembers={totalMembers}
                />
            )}
        </>
    );
};

export default GroupInfo;
