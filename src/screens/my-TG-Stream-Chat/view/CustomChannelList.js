import { useNavigation } from '@react-navigation/native';
import React, { useContext, useMemo } from 'react';
import { ChannelList, useChatContext } from 'stream-chat-react-native';

import { GlobalContext } from '../../../context/contextApi';
import { StreamChatContext } from '../../../context/StreamChatContext';
import ChannelPreviewComponent from '../TGChatComponent/ChannelPreviewComponent';
import EmptyScreen from '../TGChatComponent/EmptyScreen';

const CustomChannelList = ({
    sort,
    filters,
    options,
    searchValue,
    triggerSearchString,
    handleChannelRenderFilterFn,
    reRenderForcefully,
    handleLongPressPopupState,
    activeTab,
}) => {
    const { setActiveChannel, channel } = useChatContext();
    const { setChannel } = useContext(StreamChatContext);
    const { actions } = useContext(GlobalContext);
    const navigation = useNavigation();

    const handleOnSelect = (data) => {
        setChannel(data);
        actions?.updatedChannelAction(data);
        setActiveChannel(data);
        navigation.navigate('MessageScreen');
    };

    const memoizedFilters = useMemo(() => filters, [filters]);
    const memoizedSort = useMemo(() => sort, [sort]);
    const memoizedOptions = useMemo(() => options, [options]);

    return (
        <ChannelList
            onSelect={handleOnSelect}
            key={reRenderForcefully}
            filters={memoizedFilters}
            sort={memoizedSort}
            options={memoizedOptions}
            maxUnreadCount={10}
            EmptyStateIndicator={(props) => (
                <EmptyScreen
                    searchData={searchValue?.current}
                    {...props}
                    triggerSearchString={triggerSearchString}
                    activeTab={activeTab}
                />
            )}
            numberOfSkeletons={12}
            Preview={(data) => (
                <ChannelPreviewComponent
                    data={data}
                    handleOnSelect={handleOnSelect}
                    handleLongPressPopupState={handleLongPressPopupState}
                />
            )}
            channelRenderFilterFn={handleChannelRenderFilterFn}
            LoadingErrorIndicator={() => null}
            HeaderErrorIndicator={() => null}
            HeaderNetworkDownIndicator={() => null}
            loadingChannels={true}
        />
    );
};

export default React.memo(CustomChannelList);
