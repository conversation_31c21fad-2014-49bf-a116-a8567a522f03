import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { View, StyleSheet, useColorScheme, Text, TouchableOpacity, Platform, KeyboardAvoidingView } from 'react-native';
import { Chat, OverlayProvider } from 'stream-chat-react-native';
import moment from 'moment';
import { useIsFocused, useNavigation } from '@react-navigation/native';

import { AuthContext } from '../../../context/AuthContext';
import { StreamChatContext } from '../../../context/StreamChatContext';
import StreamChatHeader from '../../../components/layout/StreamChatHeader';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import {
    ADMIN_CREATED_GROUP,
    CHANNEL_LIMIT,
    MY_TG_GROUP,
    ONE_TO_ONE,
    STREAM_MESSAGE_LIMIT,
    SYSTEM_MESSAGE,
    SYSTEM_PRIVATE_NETWORK,
    SYSTEM_THOUSAND_GREENS_PUBLIC,
    USER_CREATED_GROUP,
} from '../client';
import StreamChatSearchInput from '../../my-TG-friends/view/StreamChatSearchInput';
import debounce, { commonFilter, handleGetAllFriendsId } from '../action';
import ArchivedIconTealNew from '../../../assets/svg/ArchivedIconTealNew.svg';
import { GlobalContext } from '../../../context/contextApi';
import CustomChannelList from './CustomChannelList';
import BottomSheetComponent from '../../../navigation/BottomTabBottomSheet';
import HomeScreenCommonHeader from '../../../components/homeScreenComponent/HomeScreenCommonHeader';
import TopNavBar from '../../../components/TopNavBar';
import { ALL, GROUP, DM } from '../../../utils/constants/strings';
import Animated, { useSharedValue, withTiming } from 'react-native-reanimated';

const tab = [
    {
        name: ALL,
    },
    {
        name: GROUP,
    },
    {
        name: DM,
    },
];

const TGChat = () => {
    const { client, setUnreadcount, addMutedChannel, mutedChannels } = useContext(StreamChatContext);
    const { user } = useContext(AuthContext);
    const { actions, state } = useContext(GlobalContext);
    const navigation = useNavigation();
    const [searchData, setSearchData] = useState('');
    const [triggerSearchString, setTriggerSearchString] = useState('');
    const searchValue = useRef(searchData);
    const [filters, setFilters] = useState();
    const [sort, setSort] = useState();
    const [options, setOptions] = useState();
    const [recentChatPopupState, setRecentChatPopupState] = useState(true);
    const [screenLoading, setScreenLoading] = useState(false);
    const [reRenderForcefully, setReRenderForcefully] = useState(0);
    const [openSearchBar, setOpenSearchBar] = useState(false);
    const [activeTab, setActiveTab] = useState(ALL);
    const isFocused = useIsFocused();
    const animatedOpacity = useSharedValue(0);

    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', handleOnFocus);
        // Return the function to unsubscribe from the event so it gets removed on unmount
        return unsubscribe;
    }, [navigation, isFocused, activeTab]);

    const handleOnFocus = () => {
        commonFilter(searchData, user, setFilters, setSort, setOptions, archiveState, activeTab);
    };

    useEffect(() => {
        handleGetAllFriendsId(user?.id, actions);
        if (client) {
            // @addMutedChannel
            client?.mutedChannels.map((mutedChannel) => {
                addMutedChannel(mutedChannel?.channel?.id);
            });
        }
    }, []);

    const { archiveState } = useMemo(() => {
        const { archiveState } = state;
        return { archiveState };
    }, [state.archiveState]);

    useEffect(() => {
        actions?.setCurrentScreenName('TGChat');
        setScreenLoading(false);
    }, [archiveState]);

    useEffect(() => {
        const messageDeletedEventListener = client.on('message.deleted', (event) => {
            commonFilter(searchData, user, setFilters, setSort, setOptions, archiveState, activeTab);
        });
        const messageNewEventListner = client.on('message.new', (event) => {
            commonFilter(searchData, user, setFilters, setSort, setOptions, archiveState, activeTab);
        });
        const eventListner = client.on('user.watching.start', (event) => {
            commonFilter(searchData, user, setFilters, setSort, setOptions, archiveState, activeTab);
        });
        const mutEventListener = client.on('notification.mutes_updated', (event) => {
            commonFilter(searchData, user, setFilters, setSort, setOptions, archiveState, activeTab);
        });
        const muteChannelEventListener = client.on('notification.channel_mutes_updated', (event) => {
            commonFilter(searchData, user, setFilters, setSort, setOptions, archiveState);
        });
        const channelVisibleEventListener = client.on('channel.visible', (event) => {
            setReRenderForcefully((prev) => prev + 1);
            commonFilter(searchData, user, setFilters, setSort, setOptions, archiveState, activeTab);
        });
        const channelUpdate = client.on('channel.updated', (event) => {
            commonFilter(searchData, user, setFilters, setSort, setOptions, archiveState, activeTab);
        });
        const userDeletedEvent = client?.on('user_deleted', (event) => {
            // update the channel list, if only 1 member is messaging channel, show Deleted User and change the DP, see web for reference
            commonFilter(searchData, user, setFilters, setSort, setOptions, archiveState, activeTab);
        });

        return () => {
            channelVisibleEventListener?.unsubscribe();
            messageDeletedEventListener?.unsubscribe();
            messageNewEventListner?.unsubscribe();
            mutEventListener?.unsubscribe();
            eventListner?.unsubscribe();
            muteChannelEventListener?.unsubscribe();
            channelUpdate?.unsubscribe();
            userDeletedEvent?.unsubscribe();
        };
    }, [client, archiveState, user?.full_name, user?.first_name, user?.username, user?.profilePhoto, activeTab]);

    const callfilter = () => {
        commonFilter(searchData, user, setFilters, setSort, setOptions, archiveState, activeTab);
    };

    useEffect(() => {
        if (archiveState) {
            const filters = {
                members: { $in: [user?.id] },
                $or: [
                    {
                        $and: [
                            {
                                last_message_at: { $lte: moment().add('1', 'hour') },
                            },
                            {
                                type: { $eq: ONE_TO_ONE },
                            },
                        ],
                    },
                    {
                        $and: [
                            {
                                type: {
                                    $in: [
                                        USER_CREATED_GROUP,
                                        SYSTEM_THOUSAND_GREENS_PUBLIC,
                                        SYSTEM_PRIVATE_NETWORK,
                                        ADMIN_CREATED_GROUP,
                                        MY_TG_GROUP,
                                    ],
                                },
                            },
                        ],
                    },
                ],
                hidden: { $eq: archiveState },
            };
            setFilters(filters);
            const sort = { last_message_at: -1 };
            setSort(sort);
            const options = { limit: CHANNEL_LIMIT, messages_limit: STREAM_MESSAGE_LIMIT };
            setOptions(options);
        }
    }, [archiveState, client]);

    useEffect(() => {}, [activeTab]);

    useEffect(() => {
        searchValue.current = searchData;
    }, [searchData]);

    useEffect(() => {
        /**
         * This event is for listening to the new messages
         * Here we will check that the message must not be from the user himself
         */
        const newMessageEventListener = async (event) => {
            if (event?.message?.user?.id !== user?.id && !mutedChannels?.includes(event?.channel_id)) {
                setUnreadcount(event?.unread_channels);
            }
        };

        /**
         * When we read the channel we have to update our UI
         * @param {*} event
         */
        const messageReadNotification = async (event) => {
            let unreadChannels = [];
            if (event?.unread_channels) {
                unreadChannels = await client.queryChannels({
                    members: { $in: [user?.id] },
                    has_unread: true,
                    type: {
                        $in: [
                            USER_CREATED_GROUP,
                            SYSTEM_THOUSAND_GREENS_PUBLIC,
                            ADMIN_CREATED_GROUP,
                            MY_TG_GROUP,
                            ONE_TO_ONE,
                        ],
                    },
                });
            }
            setUnreadcount(unreadChannels?.length);
        };

        const notificationNewMessage = client.on('message.new', newMessageEventListener);
        const notificationMessageRead = client.on('notification.mark_read', messageReadNotification);

        return () => {
            notificationNewMessage?.unsubscribe();
            notificationMessageRead?.unsubscribe();
        };
    }, [mutedChannels]);

    ///////
    useEffect(() => {
        updateTriggerSearchStringUpdate(searchData);
    }, [searchData]);

    const updateTriggerSearchStringUpdate = useCallback(
        debounce((value) => {
            setTriggerSearchString(value);
        }, 200),
        [],
    );

    //This useEffect call when search is triggered
    useEffect(() => {
        if (triggerSearchString != '') {
            const typeArray =
                activeTab === ALL
                    ? [
                          USER_CREATED_GROUP,
                          SYSTEM_THOUSAND_GREENS_PUBLIC,
                          SYSTEM_PRIVATE_NETWORK,
                          ADMIN_CREATED_GROUP,
                          MY_TG_GROUP,
                      ]
                    : activeTab === GROUP
                    ? [
                          USER_CREATED_GROUP,
                          SYSTEM_THOUSAND_GREENS_PUBLIC,
                          SYSTEM_PRIVATE_NETWORK,
                          ADMIN_CREATED_GROUP,
                          MY_TG_GROUP,
                      ]
                    : [ONE_TO_ONE];
            let filters;
            if (activeTab === ALL) {
                filters = {
                    members: { $in: [user?.id] },
                    $or: [
                        {
                            'member.user.name': { $autocomplete: searchData },
                            last_message_at: { $lte: moment().add('1', 'hour') },
                            type: ONE_TO_ONE,
                        },
                        {
                            name: { $autocomplete: searchData },
                            type: {
                                $in: typeArray,
                            },
                        },
                    ],
                    hidden: { $eq: archiveState },
                };
            } else if (activeTab === GROUP) {
                // When GROUP tab is selected, only search in group chats
                filters = {
                    members: { $in: [user?.id] },
                    name: { $autocomplete: searchData },
                    type: {
                        $in: typeArray,
                    },
                    hidden: { $eq: archiveState },
                };
            } else if (activeTab === DM) {
                // When DM tab is selected, only search in one-to-one chats
                filters = {
                    members: { $in: [user?.id] },
                    $or: [
                        {
                            'member.user.name': { $autocomplete: searchData },
                        },
                        {
                            name: { $autocomplete: searchData },
                        },
                    ],
                    type: ONE_TO_ONE,
                    hidden: { $eq: archiveState },
                };
            }
            setFilters(filters);
            const sort = { last_message_at: -1 };
            setSort(sort);
            const options = { limit: 100, messages_limit: 100 };
            setOptions(options);
        }
    }, [triggerSearchString, activeTab]);

    //UseEffect used to render all the channels when the searchData is empty and user comes on the screen
    useEffect(() => {
        if (searchData == '') {
            commonFilter(searchData, user, setFilters, setSort, setOptions, archiveState, activeTab);
        }
    }, [triggerSearchString, client, archiveState, activeTab, searchData, user]);

    const colorScheme = useColorScheme();
    var getTheme = function () {
        return {
            colors: colorScheme === 'dark' ? { black: '#FFFFFF' } : { black: '#000000' },
        };
    };
    const [theme, setTheme] = useState(getTheme());

    useEffect(() => {
        setTheme(getTheme());
    }, [colorScheme]);

    const handleChannelRenderFilterFn = (channels) => {
        return channels.filter((channel) => {
            if (channel?.type === ONE_TO_ONE) {
                if (channel?.state?.messages?.length) {
                    return true;
                } else {
                    return false;
                }
            }
            return true;
        });
    };

    const handleToggleRecentPopup = () => {
        navigation.push('RecentChatPopup', { recentChatPopupState: recentChatPopupState });
    };

    const handleLongPressPopupState = () => {
        navigation.push('LongPressPopup', { callfilter: callfilter });
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : null}
            style={styles.wrapper}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}>
            <View style={styles.wrapper}>
                <HomeScreenCommonHeader
                    title={state.archiveState ? 'Archived Chat' : 'Chat'}
                    setSearchData={setSearchData}
                    setRecentChatPopupState={setRecentChatPopupState}
                    recentChatPopupState={recentChatPopupState}
                    showSearchIcon={true}
                    showCreateChatIcon={true}
                    showTripleDot={true}
                    setOpenSearchBar={setOpenSearchBar}
                    handleToggleRecentPopup={handleToggleRecentPopup}
                    handleSearchIcon={() =>
                        setOpenSearchBar((prev) => {
                            if (prev) {
                                setSearchData('');
                                animatedOpacity.value = withTiming(0, { duration: 600 });
                            } else {
                                animatedOpacity.value = withTiming(1, { duration: 600 });
                            }
                            return !prev;
                        })
                    }
                    handleCreateChat={() => {
                        setSearchData('');
                        navigation.navigate('CreateNewChat');
                        actions?.setCurrentScreenName(null);
                    }}
                    onPressGoBack={() => {
                        actions.setIsMenuBottomSheetOpen(false);
                        actions.setMenuBottomSheetOpenIndex(0);
                        actions?.archiveListAction(false);
                    }}
                />
                <View style={styles.parentWrapper}>
                    {!archiveState && (
                        <>
                            {openSearchBar && (
                                <Animated.View style={[styles.streamInputWrapper, { opacity: animatedOpacity }]}>
                                    <StreamChatSearchInput
                                        searchState={[searchData, setSearchData]}
                                        searchBoxWrapperStyle={{ marginVertical: 0 }}
                                    />
                                </Animated.View>
                            )}
                            <TouchableOpacity
                                style={styles.archiveWrapperStyle}
                                onPress={() => {
                                    actions.setIsMenuBottomSheetOpen(false);
                                    actions.setMenuBottomSheetOpenIndex(0);
                                    setScreenLoading(!screenLoading);
                                    actions?.archiveListAction(true);
                                }}>
                                <ArchivedIconTealNew />
                                <Text style={styles.archiveTextStyle}>Archived</Text>
                            </TouchableOpacity>

                            {/* Bifurcation chip UI */}
                            <View style={styles.bifurcationChipWrapper}>
                                <TopNavBar
                                    tabList={tab}
                                    activeTab={activeTab}
                                    setActiveTab={setActiveTab}
                                    onPress={() => {
                                        setSearchData('');
                                    }}
                                />
                            </View>
                        </>
                    )}

                    <OverlayProvider>
                        <Chat client={client}>
                            <CustomChannelList
                                sort={sort}
                                filters={filters}
                                options={options}
                                searchValue={searchValue}
                                triggerSearchString={triggerSearchString}
                                handleChannelRenderFilterFn={handleChannelRenderFilterFn}
                                reRenderForcefully={reRenderForcefully}
                                handleLongPressPopupState={handleLongPressPopupState}
                                activeTab={activeTab}
                            />
                        </Chat>
                    </OverlayProvider>
                </View>
            </View>
        </KeyboardAvoidingView>
    );
};
export default TGChat;

const styles = StyleSheet.create({
    imageContainer: {
        width: 46,
        height: 46,
        borderRadius: 50,
        backgroundColor: 'rgba(9, 128, 137, 1)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageTextStyle: {
        textAlign: 'center',
        fontSize: 25,
        color: 'rgba(255, 255, 255, 1)',
        fontFamily: 'Ubuntu',
        fontWeight: '500',
    },
    textWrapper: {
        width: Spacing.SCALE_250,
        alignItems: 'center',
        marginTop: Spacing.SCALE_10,
    },
    text: {
        fontSize: Typography.FONT_SIZE_18,
        lineHeight: Size.SIZE_20,
        fontWeight: '500',
        fontFamily: 'Ubuntu',
        textAlign: 'center',
    },
    btnWrapper: {
        width: Spacing.SCALE_150,
        height: Size.SIZE_40,
        backgroundColor: 'rgba(9, 128, 137, 1)',
        borderRadius: Size.SIZE_8,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: Spacing.SCALE_10,
    },
    btnText: {
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_18,
        fontWeight: '500',
        fontFamily: 'Ubuntu',
        color: 'rgba(255, 255, 255, 1)',
    },
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.SCALE_20,
    },
    previewBox: {
        height: Size.SIZE_60,
        alignItems: 'center',
        paddingVertical: Spacing.SCALE_10,
        flexDirection: 'row',
    },
    box1: {
        marginLeft: Spacing.SCALE_10,
    },
    msgPreviewContainer: {
        marginTop: Spacing.SCALE_5,
    },
    text1: {
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_13,
        fontWeight: '400',
        color: 'rgba(102, 102, 102, 1)',
        paddingVertical: Spacing.SCALE_1,
        fontFamily: 'Ubuntu',
    },
    text2Wrapper: {
        flexDirection: 'row',
    },
    text2: {
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_13,
        fontWeight: '400',
        color: 'rgba(102, 102, 102, 1)',
        marginLeft: Spacing.SCALE_5,
        paddingVertical: Spacing.SCALE_1,
        fontFamily: 'Ubuntu',
    },
    dividerStyle: {
        height: 0.5,
        backgroundColor: 'rgba(128, 128, 128, 0.3)',
    },
    box2: {
        alignItems: 'flex-end',
    },
    streamInputWrapper: {
        paddingHorizontal: Spacing.SCALE_10,
    },
    parentWrapper: {
        flex: 1,
        backgroundColor: 'rgba(242, 242, 242, 1)',
        borderTopLeftRadius: Spacing.SCALE_10,
        borderTopRightRadius: Spacing.SCALE_10,
        // zIndex: 1,
    },
    wrapper: {
        flex: 1,
        backgroundColor: 'rgba(255, 255, 255, 1)',
    },
    archiveWrapperStyle: {
        flexDirection: 'row',
        paddingHorizontal: Spacing.SCALE_30,
        paddingTop: Spacing.SCALE_15,
        paddingBottom: Spacing.SCALE_10,
    },
    archiveTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_20,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(51, 51, 51, 1)',
        marginLeft: Spacing.SCALE_25,
    },
    loaderStyle: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        right: 0,
        left: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    bifurcationChipWrapper: {
        paddingHorizontal: Spacing.SCALE_16,
        marginBottom: Spacing.SCALE_8,
        marginTop: Spacing.SCALE_4,
    },
});
