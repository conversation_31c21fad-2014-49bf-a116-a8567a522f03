import React, { useContext, useRef } from 'react';
import {
    View,
    StyleSheet,
    Text,
    Pressable,
    ImageBackground,
    FlatList,
    Platform,
    Linking,
    TouchableOpacity,
    Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import moment from 'moment';

import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import {
    Csv,
    Doc,
    Docx,
    Generic_audio,
    Generic,
    Html,
    Md,
    Odt,
    Pdf,
    Ppt,
    Pptx,
    Rar,
    Sevenz,
    Tar_gz,
    Txt,
    Xls,
    Xlsx,
    Zip,
    Rtf,
} from '../../../../assets/svg/document';
import Download from '../../../../assets/svg/download.svg';
import VideoIcon from '../../../../assets/svg/video.svg';
import { colors } from '../../../../theme/theme';
import { handleMessageScreenParticipantsName } from '../handleMessageScreenParticipantsName';
import { GlobalContext } from '../../../../context/contextApi';
import { AuthContext } from '../../../../context/AuthContext';
import { mentionUsers } from './handleMentionedUsersUI';
import { StreamChatContext } from '../../../../context/StreamChatContext';
import { handleLinkUI } from './handleLinkUI';
import ShowPollMessage from './ShowPollMessage';

const ShowChatImages = ({ messageData, alignment, onLongPress, onPressClick, media = null, isLast = false }) => {
    const navigation = useNavigation();
    const { channel } = useContext(StreamChatContext);
    const documentFile = messageData?.attachments.filter((r) => r.type === 'file');
    const mediaFile = messageData?.attachments.filter((r) => r.type != 'file' && r.type != undefined);
    const { state } = useContext(GlobalContext);
    const { user } = useContext(AuthContext);

    const messageTime = moment.utc(messageData?.created_at).local().format('h:mm A');
    const styles = makeStyles(alignment);

    const ImageInerView = ({ url, backgroundStyle, imageStyle, videoIconShow = false, textShow = false, text }) => {
        return (
            <ImageBackground source={{ uri: url }} style={backgroundStyle} imageStyle={imageStyle}>
                <View style={{ flex: 1 }}>
                    {videoIconShow && (
                        <View
                            style={{
                                flex: 1,
                                justifyContent: 'flex-end',
                                margin: 10,
                            }}>
                            <VideoIcon />
                        </View>
                    )}

                    {textShow && (
                        <View
                            style={{
                                justifyContent: 'center',
                                position: 'absolute',
                                alignItems: 'center',
                                width: '100%',
                                height: '100%',
                                backgroundColor: colors.shadowColor,
                            }}>
                            <Text style={[styles.more]}>{'+ ' + text}</Text>
                        </View>
                    )}
                </View>
            </ImageBackground>
        );
    };

    const showDocument = () => {
        return (
            <FlatList
                data={documentFile}
                renderItem={({ item }) => (
                    <Pressable onPress={() => OpenFile(item)} onLongPress={onLongPress}>
                        <View style={styles.documentContainer}>
                            {showIcon(item)}
                            <Text style={styles.documentName} numberOfLines={2} ellipsizeMode="tail">
                                {item.title}
                            </Text>
                            <Download fill="white" style={{ marginTop: 4 }} />
                        </View>
                    </Pressable>
                )}
                keyExtractor={(item, index) => index.toString()}
            />
        );
    };

    const showIcon = (item, styleShow = false) => {
        const extension = item.title.split('.').pop();

        switch (extension) {
            case 'csv':
                return styleShow ? (
                    <Csv
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Csv />
                );
            case 'doc':
                return styleShow ? (
                    <Doc
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Doc />
                );
            case 'docx':
                return styleShow ? (
                    <Docx
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Docx />
                );
            case 'audio':
                return styleShow ? (
                    <Generic_audio
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Generic_audio />
                );
            case 'txt':
                return styleShow ? (
                    <Txt
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Txt />
                );
            case 'html':
                return styleShow ? (
                    <Html
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Html />
                );
            case 'md':
                return styleShow ? (
                    <Md
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Md />
                );
            case 'odt':
                return styleShow ? (
                    <Odt
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Odt />
                );
            case 'pdf':
                return styleShow ? (
                    <Pdf
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Pdf />
                );
            case 'ppt':
                return styleShow ? (
                    <Ppt
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Ppt />
                );
            case 'pptx':
                return styleShow ? (
                    <Pptx
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Pptx />
                );
            case 'rar':
                return styleShow ? (
                    <Rar
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Rar />
                );
            case '7z':
                return styleShow ? (
                    <Sevenz
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Sevenz />
                );
            case 'tar.gz':
                return styleShow ? (
                    <Tar_gz
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Tar_gz />
                );
            case 'zip':
                return styleShow ? (
                    <Zip
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Zip />
                );
            case 'xls':
                return styleShow ? (
                    <Xls
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Xls />
                );
            case 'xlsx':
                return styleShow ? (
                    <Xlsx
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Xlsx />
                );
            case 'rtf':
                return styleShow ? (
                    <Rtf
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Rtf />
                );
            default:
                return styleShow ? (
                    <Generic
                        style={{
                            alignSelf: 'flex-end',
                            justifyContent: 'flex-end',
                        }}
                        height={50}
                        width={54}
                    />
                ) : (
                    <Generic />
                );
        }
    };

    const OpenFile = async (item) => {
        let finalUrl = item.asset_url;
        if (Platform.OS === 'android') {
            Linking.openURL(finalUrl);
        } else {
            const supported = await Linking.canOpenURL(finalUrl);

            if (supported) {
                Linking.openURL(finalUrl);
            } else {
                console.warn(`Don't know how to open URI: ${finalUrl}`);
            }
        }
    };

    const showMedia = () => {
        const mediaTotal = mediaFile.length;
        return (
            <Pressable
                onLongPress={onLongPress}
                onPress={() => {
                    if (mediaTotal <= 3) {
                        if (media?.type === 'image') {
                            navigation.navigate('ShowUserDp', {
                                profilePhoto: media?.image_url,
                            });
                        } else {
                            navigation.navigate('VideoDetailScreen', {
                                videosUrls: media,
                            });
                        }
                    } else {
                        navigation.navigate('OpenVideoScreen', {
                            mediaFile: mediaFile,
                        });
                    }
                }}>
                <View>
                    {mediaTotal <= 3 ? (
                        <ImageInerView
                            url={media?.type === 'image' ? media?.image_url : media?.thumb_url}
                            backgroundStyle={styles.imageBackground1}
                            imageStyle={styles.imageStyle1}
                            videoIconShow={media?.type === 'image' ? false : true}
                        />
                    ) : (
                        <View style={{ flexDirection: 'row' }}>
                            <View>
                                <ImageInerView
                                    url={
                                        mediaFile[0].type === 'image' ? mediaFile[0].image_url : mediaFile[0].thumb_url
                                    }
                                    backgroundStyle={styles.imageBackground6}
                                    imageStyle={styles.imageStyle6}
                                    videoIconShow={mediaFile[0].type === 'image' ? false : true}
                                />

                                <ImageInerView
                                    url={
                                        mediaFile[2].type === 'image' ? mediaFile[2].image_url : mediaFile[2].thumb_url
                                    }
                                    backgroundStyle={styles.imageBackground7}
                                    imageStyle={styles.imageStyle8}
                                    videoIconShow={mediaFile[2].type === 'image' ? false : true}
                                />
                            </View>
                            <View>
                                <ImageInerView
                                    url={
                                        mediaFile[1].type === 'image' ? mediaFile[1].image_url : mediaFile[1].thumb_url
                                    }
                                    backgroundStyle={styles.imageBackground4}
                                    imageStyle={styles.imageStyle4}
                                    videoIconShow={mediaFile[1].type === 'image' ? false : true}
                                />

                                <ImageInerView
                                    url={
                                        mediaFile[3]?.type === 'image'
                                            ? mediaFile[3]?.image_url
                                            : mediaFile[3]?.thumb_url
                                    }
                                    backgroundStyle={styles.imageBackground5}
                                    imageStyle={styles.imageStyle5}
                                    videoIconShow={mediaFile[3]?.type === 'image' ? false : true}
                                    textShow={mediaFile.length > 4}
                                    text={mediaFile.length - 4}
                                />
                            </View>
                        </View>
                    )}
                </View>
            </Pressable>
        );
    };

    const onClickLink = (url) => {
        Linking.openURL(url.trim());
    };

    const quotedMessage = () => {
        return (
            <TouchableOpacity onLongPress={onLongPress}>
                <TouchableOpacity onPress={onPressClick}>
                    <View style={styles.quotedContainer}>
                        <View style={styles.quoteDivider}></View>
                        <View style={{ flexDirection: 'row', width: '100%' }}>
                            <View
                                style={{
                                    width: messageData?.quoted_message?.attachments?.length > 0 ? '75%' : '100%',
                                }}>
                                <Text style={styles.quoteName}>
                                    {handleMessageScreenParticipantsName(
                                        state?.allFriendsId,
                                        messageData?.quoted_message?.user,
                                        user,
                                    )}
                                </Text>
                                {messageData?.quoted_message?.text && messageData?.quoted_message?.text !== '' ? (
                                    <Text
                                        style={[styles.quoteName, { paddingTop: 2, paddingBottom: 5 }]}
                                        numberOfLines={2}
                                        ellipsizeMode="tail">
                                        {messageData?.quoted_message?.text}
                                    </Text>
                                ) : messageData?.quoted_message?.attachments?.length > 0 ? (
                                    <>
                                        <Text
                                            style={[
                                                styles.quoteName,
                                                {
                                                    paddingTop: 2,
                                                    paddingBottom: 5,
                                                },
                                            ]}
                                            numberOfLines={2}
                                            ellipsizeMode="tail">
                                            {messageData?.quoted_message?.attachments[0]?.type}
                                        </Text>
                                    </>
                                ) : (
                                    <Text
                                        style={[styles.quoteName, { paddingTop: 2, paddingBottom: 5 }]}
                                        numberOfLines={2}
                                        ellipsizeMode="tail">
                                        Quoted message
                                    </Text>
                                )}
                            </View>
                            {messageData?.quoted_message?.attachments?.length > 0 && (
                                <View
                                    style={{
                                        flex: 0.9,
                                        justifyContent: 'center',
                                    }}>
                                    {messageData?.quoted_message?.attachments[0].type === 'file' ? (
                                        showIcon(messageData?.quoted_message?.attachments[0], true)
                                    ) : (
                                        <Image
                                            source={{
                                                uri:
                                                    messageData?.quoted_message?.attachments[0].type === 'image'
                                                        ? messageData?.quoted_message?.attachments[0].image_url
                                                        : messageData?.quoted_message?.attachments[0].thumb_url,
                                            }}
                                            style={{
                                                height: 50,
                                                width: 50,
                                                borderRadius: 4,
                                                alignSelf: 'flex-end',
                                                justifyContent: 'flex-end',
                                            }}
                                        />
                                    )}
                                </View>
                            )}
                        </View>
                    </View>
                </TouchableOpacity>
            </TouchableOpacity>
        );
    };

    return (
        <View>
            {messageData?.quoted_message || messageData?.quoted_message_id || messageData?.reply_to
                ? quotedMessage()
                : null}
            {mediaFile.length > 0 ? showMedia() : <></>}
            {documentFile.length > 0 ? showDocument() : <></>}
            <View>
                {messageData?.text && (
                    <Pressable onLongPress={onLongPress}>
                        {/* show message text */}
                        {messageData?.html.includes('href') && messageData?.html?.includes('<a') ? (
                            handleLinkUI(messageData, onClickLink, alignment)
                        ) : messageData?.mentioned_users?.length ? (
                            mentionUsers(messageData, state?.allFriendsId, navigation, channel, alignment, user)
                        ) : messageData?.poll_id ? (
                            <ShowPollMessage messageData={messageData} alignment={alignment} />
                        ) : media ? (
                            isLast ? (
                                <Text style={styles.textStyle}>{messageData?.text}</Text>
                            ) : null
                        ) : (
                            <Text style={styles.textStyle}>{messageData?.text}</Text>
                        )}
                    </Pressable>
                )}
                <Text
                    style={[
                        styles.time,
                        {
                            marginVertical: mediaFile.length === 1 ? Spacing.SCALE_8 : null,
                        },
                    ]}>
                    {messageTime}
                </Text>
            </View>
        </View>
    );
};

const makeStyles = (alignment) =>
    StyleSheet.create({
        textStyle: {
            color: alignment === 'left' ? colors.lightBlack : colors.whiteColor,
            marginTop: Spacing.SCALE_4,
            marginLeft: 2,
            paddingHorizontal: Spacing.SCALE_8,
            fontSize: Typography.FONT_SIZE_14,
            fontFamily: 'Ubuntu-Medium',
            lineHeight: Size.SIZE_20,
        },
        time: {
            color: alignment === 'left' ? colors.lightBlack : colors.whiteColor,
            alignSelf: 'flex-end',
            fontSize: Typography.FONT_SIZE_10,
            marginBottom: Spacing.SCALE_8,
            fontFamily: 'Ubuntu-Medium',
            marginRight: alignment === 'left' ? Spacing.SCALE_12 : Spacing.SCALE_6,
        },
        more: {
            color: 'white',
            fontWeight: '700',
            fontSize: Typography.FONT_SIZE_18,
            fontFamily: 'Ubuntu-Bold',
            color: colors.whiteColor,
        },
        quotedContainer: {
            backgroundColor: alignment === 'left' ? '#F2F2F2' : '#096269',
            width: alignment === 'left' ? Size.SIZE_239 : Size.SIZE_280,
            alignSelf: alignment === 'left' ? 'flex-start' : 'flex-end',
            borderTopLeftRadius: Spacing.SCALE_8,
            borderTopRightRadius: Spacing.SCALE_8,
            borderBottomLeftRadius: Spacing.SCALE_8,
            borderBottomRightRadius: Spacing.SCALE_8,
            margin: 4,
            flexDirection: 'row',
            minHeight: 54,
            marginBottom: 8,
        },
        quoteName: {
            color: alignment === 'left' ? '#333333' : '#F2F2F2',
            paddingTop: Spacing.SCALE_5,
            paddingHorizontal: Spacing.SCALE_11,
            fontSize: Typography.FONT_SIZE_12,
            fontFamily: 'Ubuntu-Medium',
            lineHeight: 20,
        },
        documentName: {
            flex: 1,
            color: '#333333',
            paddingHorizontal: Spacing.SCALE_11,
            fontSize: Typography.FONT_SIZE_12,
            fontFamily: 'Ubuntu-Medium',
            lineHeight: 20,
        },
        documentContainer: {
            backgroundColor: '#F2F2F2',
            width: alignment === 'left' ? Size.SIZE_239 : Size.SIZE_280,
            alignSelf: 'flex-start',
            borderTopLeftRadius: Spacing.SCALE_8,
            borderTopRightRadius: Spacing.SCALE_8,
            borderBottomLeftRadius: Spacing.SCALE_8,
            borderBottomRightRadius: Spacing.SCALE_8,
            margin: 4,
            flexDirection: 'row',
            minHeight: 54,
            padding: 7,
        },
        imageBackground1: {
            width: alignment === 'left' ? Size.SIZE_239 : Size.SIZE_280,
            height: 250,
            marginTop: 5,
            marginLeft: 5,
            marginRight: 5,
        },
        imageBackground2: {
            width: alignment === 'left' ? Size.SIZE_119 : Size.SIZE_140,
            height: 250,
            marginTop: 5,
            marginLeft: 5,
            marginRight: 1,
        },
        imageBackground3: {
            width: alignment === 'left' ? Size.SIZE_119 : Size.SIZE_140,
            height: 250,
            marginTop: 5,
            marginLeft: 1,
            marginRight: 5,
        },
        imageBackground4: {
            width: alignment === 'left' ? Size.SIZE_119 : Size.SIZE_140,
            height: 124,
            marginTop: 5,
            marginLeft: 1,
            marginRight: 5,
        },
        imageBackground5: {
            width: alignment === 'left' ? Size.SIZE_119 : Size.SIZE_140,
            height: 124,
            marginRight: 5,
            marginLeft: 1,
            marginTop: 2,
        },
        imageBackground6: {
            width: alignment === 'left' ? Size.SIZE_119 : Size.SIZE_140,
            height: 124,
            marginTop: 5,
            marginLeft: 5,
            marginRight: 1,
        },
        imageBackground7: {
            width: alignment === 'left' ? 128 : Size.SIZE_140,
            height: 124,
            marginTop: 2,
            marginLeft: 5,
            marginRight: 1,
        },
        imageStyle1: {
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
            borderBottomLeftRadius: 8,
            borderBottomRightRadius: 8,
        },
        imageStyle2: {
            borderTopLeftRadius: 8,
            borderBottomLeftRadius: 8,
        },
        imageStyle3: {
            borderTopRightRadius: 8,
            borderBottomRightRadius: 8,
        },
        imageStyle4: {
            borderTopRightRadius: 8,
        },
        imageStyle5: {
            borderBottomRightRadius: 8,
        },
        imageStyle6: {
            borderTopLeftRadius: 8,
        },
        imageStyle8: {
            borderBottomLeftRadius: 8,
        },
        linkMessage: {
            color: alignment === 'left' ? colors.linkColor : colors.linkSenderMessageColor,
            paddingTop: Spacing.SCALE_10,
            paddingHorizontal: Spacing.SCALE_8,
            fontSize: Typography.FONT_SIZE_14,
            fontFamily: 'Ubuntu-Medium',
            lineHeight: Size.SIZE_20,
        },
        quoteDivider: {
            backgroundColor: '#0AB4C0',
            width: 5,
            borderTopLeftRadius: Spacing.SCALE_4,
            borderBottomLeftRadius: Spacing.SCALE_4,
        },
    });

export default ShowChatImages;
