import { useIsFocused, useNavigation } from '@react-navigation/native';
import { ActivityIndicator, Alert, Image, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { Platform, View } from 'react-native';
import { memo, useContext, useEffect, useMemo, useState } from 'react';
import { StreamChatContext } from '../../../context/StreamChatContext';
import { messageActions as defaultMessageActions, useMessageInputContext } from 'stream-chat-react-native';
import { createChatChannel } from './action/createChatChannel';
import Copy from '../../../assets/svg/Copy.svg';

import {
    Channel,
    Chat,
    MessageInput,
    MessageList,
    OverlayProvider,
    Streami18n,
    ThemeProvider,
    useMessageContext,
} from 'stream-chat-react-native';
import HandleDateHeader from '../../my-TG-Stream-Chat/TGChatComponent/DateHeaderComponent';
import HandleDateSeperator from '../../my-TG-Stream-Chat/TGChatComponent/DateSeperatorComponent';
import StreamButton from '../../my-TG-Stream-Chat/TGChatComponent/StreamSendCustomButton';
import ShowChatMessage from '../../my-TG-Stream-Chat/view/ShowChatMessage';
import { getStreamChannel } from '../../myTgGroup/helperFunctions/querryChannel';
import { GlobalContext } from '../../../context/contextApi';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import StreamEmptyScreen from '../../my-TG-Stream-Chat/view/StreamEmptyScreen';
import { CustomMessageActionListItem } from '../../my-TG-Stream-Chat/TGChatComponent/CustomMessageActionListItem';
import Clipboard from '@react-native-clipboard/clipboard';
import { colors } from '../../../theme/theme';
import useClient from '../../../hooks/useClient';
import { AuthContext } from '../../../context/AuthContext';
import { GET_REQUEST_CHAT, UPDATE_REQUEST_CHAT, UPDATE_REQUESTER_CHAT } from '../../../graphql/mutations/chat';
import { newChatNotification } from '../../../service/EndPoint';
import { sendNotification } from '../../../utils/notification/createNotification';
import DeleteConfirmationPopup from '../../my-TG-Stream-Chat/view/DeleteConfirmationPopup';
import { DELETED_USER_TEXT, FROZEN_CHANNEL_TEXT } from '../../my-TG-Stream-Chat/client';
import rollbar from '../../../utils/rollbar/rollbar';
import { updateMessageKey } from './action/updateMessageKey';

const RequestChatScreen = ({
    requestId = '',
    requestorUserId = '',
    hostUserId = '',
    gameId = '',
    hasMessages = false,
    streamChannelId = '',
    isDeletedUser = false,
}) => {
    const navigation = useNavigation();
    const [deletePopup, setDeletePopup] = useState(false);
    const { actions, state } = useContext(GlobalContext);
    const graphQlClient = useClient();
    const { user } = useContext(AuthContext);

    const { client, setChannel, chatClient, channel } = useContext(StreamChatContext);
    const { selectedMessageId } = state;
    const [showForwardPopup, setShowForwardPopup] = useState(false);
    const [selectedData, setSelectedData] = useState([]);
    const [loadingChat, setLoadingChat] = useState(false);
    const [loading, setLoading] = useState(false);
    const [createdById, setCreatedById] = useState('');
    const isFocused = useIsFocused();
    useEffect(() => {
        if (channel?.data?.created_by?.id) {
            setCreatedById(channel?.data?.created_by?.id);
        }
    }, [channel?.data?.created_by?.id]);

    useEffect(() => {
        if (!client) {
            rollbar.error('Error for messageNewEventListener client not found');
            return;
        }
        const messageNewEventListener = client.on('message.new', async (event) => {
            try {
                if (event?.message?.type === 'system') {
                    return;
                }
                if (!createdById) {
                    const response = await graphQlClient.request(GET_REQUEST_CHAT, {
                        requestId,
                        channelId: event?.channel_id,
                    });
                    if (response?.request_chat[0]?.club_member_id) {
                        setCreatedById(response?.request_chat[0]?.club_member_id);
                        rollbar.info(
                            'Create ID is set from graphQl query GET_REQUEST_CHAT',
                            response?.request_chat[0]?.club_member_id,
                        );
                    }
                }
                await updateMessageKey({ graphQlClient, hasMessages, createdById, user, requestId, rollbar });
                const params = {
                    request: { id: requestId, game_id: gameId, user: { id: requestorUserId } },
                    user,
                    channelURL: channel?.data?.id || event?.channel_id,
                    host_id: hostUserId !== user?.id ? hostUserId : requestorUserId,
                };
                sendNotification(newChatNotification, params);
            } catch (error) {
                rollbar.error('Error for messageNewEventListener unsubscribed', error);
            }
        });

        return () => {
            messageNewEventListener?.unsubscribe();
        };
    }, [client, createdById]);

    useEffect(() => {
        if (streamChannelId) {
            setLoadingChat(true);
            const loadChannel = async () => {
                try {
                    await getStreamChannel(client, hostUserId || requestorUserId, streamChannelId, setChannel, actions);
                    setLoadingChat(false);
                } catch (error) {
                    console.log('Error loading channel:', error);
                    setLoadingChat(false);
                }
            };
            loadChannel();
        } else {
            const createRequestChannel = async () => {
                try {
                    setLoadingChat(true);
                    const response = await createChatChannel({
                        request_id: requestId,
                        requestor_id: requestorUserId,
                        host_id: hostUserId,
                        channel_name: `Game id: ${gameId}`,
                    });

                    if (response?.status) {
                        await getStreamChannel(client, hostUserId, response?.data?.channel_url, setChannel, actions);
                    }
                    setLoadingChat(false);
                } catch (error) {
                    setLoadingChat(false);
                    console.log('🚀 ~ createRequestChannel ~ error:', error, client);
                }
            };

            createRequestChannel();
        }

        // Don't clear channel on focus changes
        return () => {};
    }, []);

    // Add a separate effect for component unmount
    useEffect(() => {
        return () => {
            // This will only run when the component is completely unmounted
            navigation.addListener('beforeRemove', () => {
                setChannel({});
            });
        };
    }, []);

    const handleYesButton = async () => {
        setLoading(true);
        await client.deleteMessage(selectedMessageId);
        setLoading(false);
        setDeletePopup(false);
    };

    const handleEmptyState = () => {
        return <StreamEmptyScreen screenText={`Send a message`} screen={''} />;
    };

    const handleMessageAction = (param) => {
        const { isMyMessage, ownCapabilities, dismissOverlay, message, actionType, deleteMessage, editMessage } = param;
        let actions = defaultMessageActions({ ...param });
        actions = actions.filter((action) => ['quotedReply', 'deleteMessage'].includes(action?.actionType));

        if (message?.attachments?.length === 0 || !message?.attachments[0]?.type) {
            actions.push({
                action: async () => {
                    dismissOverlay();
                    Clipboard.setString(message?.text);
                },
                actionType: 'copyMessage',
                icon: <Copy />,
                title: 'Copy Message',
            });
        }

        return actions;
    };

    const CustomMessageUIComponent = (params) => {
        const messageData = useMessageContext();
        const { message, reactions, onLongPress, alignment, onPress } = messageData;

        return (
            <ShowChatMessage
                message={message}
                alignment={alignment}
                onLongPress={onLongPress}
                onPressClick={onPress}
                reactions={reactions}
            />
        );
    };
    return (
        <>
            <View style={{ flex: 1 }}>
                <OverlayProvider
                    i18nInstance={Streami18n}
                    MessageActionListItem={(data) => CustomMessageActionListItem(data, setDeletePopup)}>
                    <Chat client={client}>
                        {loadingChat ? (
                            <View style={{ flex: 1, justifyContent: 'center' }}>
                                <ActivityIndicator size={'large'} color={colors.darkteal} />
                            </View>
                        ) : channel?.data?.id ? (
                            <MessageView
                                StreamButton={() => (
                                    <StreamButton
                                        graphQlClient={graphQlClient}
                                        hasMessages={hasMessages}
                                        createdById={createdById}
                                        user={user}
                                        requestId={requestId}
                                        rollbar={rollbar}
                                    />
                                )}
                                HandleDateHeader={HandleDateHeader}
                                handleEmptyState={handleEmptyState}
                                handleMessageAction={handleMessageAction}
                                handleDateSeparator={HandleDateSeperator}
                                channel={channel}
                                MessageSimple={CustomMessageUIComponent}
                                isDeletedUser={isDeletedUser}
                            />
                        ) : (
                            !loadingChat &&
                            channel === undefined && (
                                <View style={styles.deletedTextWrapper}>
                                    <Text style={styles.deletedText}>{DELETED_USER_TEXT}</Text>
                                </View>
                            )
                        )}
                    </Chat>
                </OverlayProvider>
            </View>
            {deletePopup && (
                <DeleteConfirmationPopup
                    deletePopup={deletePopup}
                    setDeletePopup={setDeletePopup}
                    handleYesButton={handleYesButton}
                    btnLoader={[loading, setLoading]}
                    popupTitle={`Are you sure you want to delete this message ?`}
                    popupHeader={`Delete Message`}
                />
            )}
        </>
    );
};

export default memo(RequestChatScreen);

const MessageView = ({
    StreamButton,
    channel,
    MessageSimple,
    handleEmptyState,
    handleDateSeparator,
    handleMessageAction,
    isDeletedUser,
}) => {
    const theme = {
        messageList: {
            container: {
                backgroundColor: 'rgba(242, 242, 242, 1)',
            },
        },
    };

    const MessageInputHandling = () => {
        if (isDeletedUser) {
            return (
                <Text style={[styles.deletedText, { backgroundColor: colors.greyRgba, padding: Spacing.SCALE_12 }]}>
                    {DELETED_USER_TEXT}
                </Text>
            );
        } else if (channel.data.frozen) {
            return (
                <Text style={[styles.deletedText, { backgroundColor: colors.greyRgba, padding: Spacing.SCALE_12 }]}>
                    {FROZEN_CHANNEL_TEXT}
                </Text>
            );
        } else {
            return <MessageInput additionalTextInputProps={{ fontSize: Typography.FONT_SIZE_14, lineHeight: 20 }} />;
        }
    };

    // Reusable Row Component
    const InfoRow = ({ label, value }) => (
        <View style={styles.row}>
            <Text style={[styles.systemMessageText, { flex: 1 }]}>{label}</Text>
            <Text
                style={[
                    styles.systemMessageText,
                    {
                        color: colors.lightBlack,
                        flex: 2,
                        textAlign: 'right',
                    },
                ]}>
                {value}
            </Text>
        </View>
    );

    const HandleSystemMessage = ({ message }) => {
        if (message?.text?.length) {
            return (
                <View style={styles.systemMessage}>
                    <View style={styles.systemMessageWrapper}>
                        <Text style={styles.systemMessageText}>{message.text}</Text>
                    </View>
                </View>
            );
        } else {
            return (
                <View style={styles.container}>
                    <Text style={styles.title}>Game Information</Text>
                    <InfoRow label="Guest Fee" value={message?.game_information.guestFee} />
                    <InfoRow label="Caddie required" value={message?.game_information.caddieRequired} />
                    <InfoRow label="Dress code" value={message?.game_information.dressCode} />
                    <InfoRow label="Method of fee settlement" value={message?.game_information.methodOfFeeSettlement} />
                    <InfoRow label="Other information" value={message?.game_information.otherInformation} />
                </View>
            );
        }
    };

    return (
        <>
            <View>
                <ThemeProvider style={theme}>
                    {
                        <Channel
                            key={channel?.data?.cid}
                            MessageSimple={MessageSimple}
                            channel={channel}
                            SendButton={StreamButton}
                            DateHeader={() => null}
                            EmptyStateIndicator={handleEmptyState}
                            CommandsButton={() => null}
                            messageActions={handleMessageAction}
                            disableIfFrozenChannel={false}
                            initialScrollToFirstUnreadMessage={true}
                            MessageSystem={HandleSystemMessage}
                            LoadingErrorIndicator={() => null}
                            keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : undefined}>
                            <MessageList InlineDateSeparator={handleDateSeparator} isListActive={true} />
                            <MessageInputHandling />
                        </Channel>
                    }
                </ThemeProvider>
            </View>
        </>
    );
};

const styles = StyleSheet.create({
    systemMessage: {
        paddingHorizontal: Spacing.SCALE_24,
        paddingVertical: Spacing.SCALE_5,
        alignSelf: 'center',
    },
    systemMessageWrapper: {
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_8,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.darkGreySystemMessage,
        alignItems: 'center',
        justifyContent: 'center',
    },
    systemMessageText: {
        textAlign: 'left',
        fontSize: Typography.FONT_SIZE_10,
        color: colors.systemMessageText,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_15,
    },
    container: {
        padding: 16,
        backgroundColor: colors.darkGreySystemMessage,
        borderRadius: 8,
        margin: Spacing.SCALE_24,
    },
    title: {
        fontSize: Typography.FONT_SIZE_10,
        fontWeight: '500',
        marginBottom: Spacing.SCALE_10,
        lineHeight: Size.SIZE_12,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: Spacing.SCALE_4,
    },
    deletedTextWrapper: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: Spacing.SCALE_14,
        position: 'absolute',
        bottom: 0,
    },
    deletedText: {
        color: colors.greyRgb,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        lineHeight: Size.SIZE_20,
        fontFamily: 'Ubuntu-Medium',
        textAlign: 'center',
    },
    deletedText: {
        color: 'rgba(102, 102, 102, 1)',
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        lineHeight: Size.SIZE_20,
        fontFamily: 'Ubuntu-Medium',
        textAlign: 'center',
    },
});
