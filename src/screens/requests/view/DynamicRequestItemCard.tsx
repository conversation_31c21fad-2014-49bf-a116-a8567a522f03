import { StyleSheet, Text, View, TouchableOpacity, Image, FlatList, Platform, Alert, Pressable } from 'react-native';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import moment from 'moment';

import {
    ClubGolfIconTeal,
    FirstTimeRequester,
    CircleCrossIcon,
    ChatTealNewIcon,
    ChatWhiteIcon,
    EditIcon,
    HostIcon,
    IsTgFounderClubMemberIcon,
} from '../../../assets/svg';
import { RequestedAcceptedRequest } from '../../../interface';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { handleTimeFormat } from '../../../components/timeFormatComponent/handleTimeFormat';
import TealButtonNew from '../../../components/buttons/TealButtonNew';
import showToast from '../../../components/toast/CustomToast';
import { RootStackParamList } from '../../../interface/type';
import { getAllRequest } from '../action/getAllRequest';
import { GlobalContext } from '../../../context/contextApi';
import { AuthContext } from '../../../context/AuthContext';
import { ACCEPTED, RECEIVED, REQUESTED } from '../../../utils/constants/strings';
import HostPreview from './HostPreview';
import { calculateGuestTimeRestrictionDates } from '../../../utils/helpers/CreateClubHelper';
import { FETCH_INTERNATIONAL_CLUB_DURATION, FETCH_LOACAL_CLUB_DURATION } from '../../../graphql/queries/clubDuration';
import useQuery from '../../../hooks/useQuery';
import GET_CLUB_DATA from '../../../graphql/queries/getClubData';
import useClient from '../../../hooks/useClient';
import AcceptUserCard from './AcceptUserCard';
import { Check } from '../../../assets/images/svg';
import redirectToUserProfile from '../action/openUserProfile';
import { StreamChatContext } from '../../../context/StreamChatContext';

const DynamicRequestItemCard: React.FC<{
    item: RequestedAcceptedRequest;
    title: string;
    type: string;
    setAcceptRequestModal?: (modal: { request: any; onRefresh: () => void } | null) => void;
    acceptRequestModal?: { request: any; onRefresh: () => void } | null;
    setIsVisible?: (visible: boolean) => void;
    isVisible?: boolean;
    isAccepted?: boolean;
    setIsAccepted?: (accepted: boolean) => void;
    setDateRangeModal?: (modal: any) => void;
    dateRangeModal?: any;
    setDeclineRequest?: (request: any) => void;
    declineRequest?: any;
    setReviewModal?: (modal: any) => void;
    reviewModal?: any;
    setIsMyRequest: (isMyRequest: boolean) => void;
    isMyRequest: boolean;
    setShowAcceptRequestPopupInfo?: (showAcceptRequestPopupInfo: object) => void;
}> = ({
    item,
    type,
    setAcceptRequestModal,
    setIsVisible,
    setDateRangeModal,
    setDeclineRequest,
    setReviewModal,
    setIsMyRequest,
    setShowAcceptRequestPopupInfo,
}) => {
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { user } = useContext(AuthContext);
    const client = useClient();
    const { setChannel } = useContext(StreamChatContext);
    const { actions, state } = useContext(GlobalContext);
    const { unreadChannels, unreadChannelsObject } = state;
    const [club, setClub] = useState<any>();
    const [disabledDates, setDisabledDates] = useState<any[]>([]);
    const {
        game_id,
        stream_channel_id,
        request_id,
        requestor_user_id,
        host_user_id,
        has_messages,
        requestor_full_name,
        requester_has_message,
        message,
        is_first_request,
        club_id,
        club_name,
        start_date,
        end_date,
        game_date,
        number_of_players,
        requestor_profile_photo,
        requestor_username,
    } = useMemo(() => {
        const {
            game_id,
            stream_channel_id,
            request_id,
            requestor_user_id,
            host_user_id,
            has_messages,
            requestor_full_name,
            requester_has_message,
            message,
            is_first_request,
            club_id,
            club_name,
            start_date,
            end_date,
            game_date,
            number_of_players,
            requestor_profile_photo,
            requestor_username,
        } = item;
        return {
            game_id,
            stream_channel_id,
            request_id,
            requestor_user_id,
            host_user_id,
            has_messages,
            requestor_full_name,
            requester_has_message,
            message,
            is_first_request,
            club_id,
            club_name,
            start_date,
            end_date,
            game_date,
            number_of_players,
            requestor_profile_photo,
            requestor_username,
        };
    }, [item]);

    let localClub = useQuery(FETCH_LOACAL_CLUB_DURATION);
    let internationalClub = useQuery(FETCH_INTERNATIONAL_CLUB_DURATION);
    let month =
        club?.country_code == user?.phone_number_details?.countryCode
            ? localClub?.data?.system_setting[0]?.value?.value
            : internationalClub?.data?.system_setting[0]?.value?.value;
    const { data } = useQuery(GET_CLUB_DATA, {
        club_id: club_id,
    });

    const handleType = () => {
        return type === RECEIVED
            ? 'received-open'
            : type === REQUESTED
            ? 'requested-open'
            : type === ACCEPTED
            ? item.requestor_user_id === user.id
                ? `requested-${type.toLowerCase()}`
                : `received-${type.toLowerCase()}`
            : type.toLowerCase();
    };

    useEffect(() => {
        const getClubDetails = async () => {
            // @ts-ignore
            const { view_club_search } = await client.request(`{
            view_club_search(where: {id: {_eq: ${club_id}}}) {
                id
                name
                user_array
                guest_time_restrictions
                closure_period
                guest_fee,
                country_code
            }
        }`);
            const club = view_club_search?.length > 0 ? view_club_search[0] : null;
            setClub(club);
            if (club?.closure_period) {
                const dates = calculateGuestTimeRestrictionDates(
                    club?.closure_period,
                    club?.guest_time_restrictions,
                    month * 12,
                );
                setDisabledDates(dates);
            }
        };
        getClubDetails();
    }, [month]);

    const hostData = useMemo(() => {
        return state.allRequestHostsData?.find((data: any) => data.request_id === item.request_id)?.hosts || [];
    }, [item, state.allRequestHostsData]);

    const goToChatScreen = () => {
        if (host_user_id && requestor_user_id && host_user_id !== requestor_user_id) {
            navigation.navigate('ChatDetails', {
                type: handleType(),
                request_id: request_id,
                game_id: game_id,
                streamChannelId: stream_channel_id,
                requestor_user_id: requestor_user_id,
                requestor_full_name: requestor_full_name,
                host_user_id: host_user_id,
                has_messages: has_messages,
            });
        } else {
            showToast({});
        }
    };

    const showRequestPopup = () => {
        if (user?.additional_settings?.showAcceptRequestPopup) {
            setIsVisible && setIsVisible(true);
        } else {
            navigation.navigate('RequestConfirmScreen', {
                game_id: game_id,
                requestor_full_name: requestor_full_name,
                acceptRequest: true,
                type: handleType(),
                chatAgainCallBack: goToChatScreen,
                request: item,
                handleYesButton: () => {
                    setAcceptRequestModal &&
                        setAcceptRequestModal({
                            request: item,
                            onRefresh: () => getAllRequest(actions, user?.id),
                        });
                },
            });
        }
    };

    const handleEditRequest = () => {
        navigation.navigate('Create Request', {
            request: item,
        });
    };

    const handleDelete = (hostData: any[]) => {
        if (
            handleType() === 'received-accepted' ||
            handleType() === 'received-open' ||
            handleType() === 'requested-accepted' ||
            handleType() === 'requested-open'
        ) {
            navigation.navigate('RequestConfirmScreen', {
                game_id: game_id,
                requestor_full_name: requestor_full_name,
                declineRequest: true,
                request_id: request_id,
                type: handleType(),
                request: item,
                showReason: true,
                hostData: hostData,
            });
        } else {
            navigation.navigate('RequestConfirmScreen', {
                game_id: game_id,
                requestor_full_name: requestor_full_name,
                declineRequest: true,
                request_id: request_id,
                type: handleType(),
                request: item,
            });
        }
    };

    const handleAcceptRequest = () => {
        setShowAcceptRequestPopupInfo &&
            setShowAcceptRequestPopupInfo({
                game_id: game_id,
                requestor_full_name: requestor_full_name,
                acceptRequest: true,
                type: handleType(),
                chatAgainCallBack: () => {
                    setChannel({});
                    goToChatScreen();
                },
                request: item,
                handleYesButton: () => {
                    setAcceptRequestModal &&
                        setAcceptRequestModal({
                            request: item,
                            onRefresh: () => getAllRequest(actions, user?.id),
                        });
                },
            });
        if (has_messages && requester_has_message) {
            showRequestPopup();
        } else {
            navigation.navigate('RequestConfirmScreen', {
                game_id: game_id,
                requestor_full_name: requestor_full_name,
                acceptRequest: false,
                chatAgainCallBack: goToChatScreen,
                request: item,
                handleYesButton: () => {},
                type: handleType(),
            });
        }
    };

    const handleEditDate = () => {
        setDateRangeModal &&
            setDateRangeModal({
                maxDate: moment()
                    .add(
                        club?.country_code?.toLowerCase() === user?.phone_number_details?.countryCode?.toLowerCase()
                            ? localClub?.data?.system_setting[0]?.value?.value * 12
                            : internationalClub?.data?.system_setting[0]?.value?.value * 12,
                        'months',
                    )
                    .format('YYYY-MM-DD'),
                disabledDates: disabledDates,
                game_id: game_id,
                request_id: request_id,
            });
    };

    const handleMarkComplete = (item: RequestedAcceptedRequest) => {
        setDeclineRequest && setDeclineRequest(item);
        setReviewModal && setReviewModal(true);
        setIsMyRequest(item.requestor_user_id === user.id);
    };

    return (
        <>
            <TouchableOpacity
                onPress={() => {
                    navigation.navigate('RequestDetailScreen', {
                        request: item,
                        type: handleType(),
                        isMyRequest: item.requestor_user_id === user.id,
                    });
                }}
                style={[
                    styles.container,
                    {
                        width: (is_first_request || user?.is_tg_founder) && type === RECEIVED ? '99%' : '100%',
                    },
                ]}
                key={club_id}>
                {(type === REQUESTED || type === ACCEPTED) && (
                    <View style={[styles.headerRightContainer, { justifyContent: 'space-between' }]}>
                        <Text style={styles.clubIdStyle}>#{game_id}</Text>
                        <View style={{ flexDirection: 'row', columnGap: Spacing.SCALE_12 }}>
                            {requestor_user_id === user.id && type === ACCEPTED ? null : (
                                <TouchableOpacity
                                    disabled={item?.host_completed || item?.requestor_completed}
                                    style={[
                                        styles.buttonWrapper,
                                        { opacity: !item?.host_completed && !item?.requestor_completed ? 1 : 0.3 },
                                    ]}
                                    onPress={() => {
                                        if (type === ACCEPTED) {
                                            handleEditDate();
                                        } else {
                                            handleEditRequest();
                                        }
                                    }}>
                                    <EditIcon />
                                </TouchableOpacity>
                            )}
                            <TouchableOpacity
                                disabled={item?.host_completed || item?.requestor_completed}
                                style={[
                                    styles.buttonWrapper,
                                    { opacity: !item?.host_completed && !item?.requestor_completed ? 1 : 0.3 },
                                ]}
                                onPress={() => handleDelete(hostData)}>
                                <CircleCrossIcon width={Size.SIZE_16} height={Size.SIZE_16} />
                            </TouchableOpacity>
                        </View>
                    </View>
                )}
                <View style={[styles.headerContainer, is_first_request && { marginTop: Spacing.SCALE_4 }]}>
                    <View style={styles.headerLeftContainer}>
                        <ClubGolfIconTeal />
                        <View style={{ rowGap: Spacing.SCALE_4 }}>
                            <Text style={styles.headerLeftText} numberOfLines={1}>
                                {club_name}
                            </Text>
                            <Text style={styles.dateStyle}>
                                {type === ACCEPTED
                                    ? `${handleTimeFormat(game_date)}`
                                    : start_date === end_date
                                    ? handleTimeFormat(start_date)
                                    : `${handleTimeFormat(start_date)} - ${handleTimeFormat(end_date)}`}
                            </Text>
                        </View>
                    </View>
                    {type === RECEIVED && (
                        <View style={styles.headerRightContainer}>
                            <Text style={styles.clubIdStyle}>#{game_id}</Text>
                        </View>
                    )}
                </View>
                {type === ACCEPTED && (
                    <>
                        {requestor_user_id === user.id ? (
                            <Text style={styles.acceptingHostsText}>{'Accepting Hosts'}</Text>
                        ) : null}
                        <AcceptUserCard
                            isRequester={requestor_user_id === user.id}
                            item={item}
                            hostData={hostData}
                            unreadChannelsObject={unreadChannelsObject}
                            hostDataItem={hostData[0]}
                            navigation={navigation}
                        />
                        <View style={{ marginTop: Spacing.SCALE_12 }} />
                        {requestor_user_id === user.id && hostData?.length > 1 ? (
                            <>
                                <Text style={styles.otherHostsText}>{'Other Hosts'}</Text>
                                <View
                                    style={{
                                        paddingHorizontal: Spacing.SCALE_12,
                                        paddingVertical: Spacing.SCALE_10,
                                        backgroundColor: colors.lightgray,
                                        borderRadius: Size.SIZE_12,
                                    }}>
                                    {hostData?.length > 0 &&
                                        hostData
                                            ?.filter((data: any) => data.id !== item.game_host_user_id)
                                            ?.map((item: any, index: number) => (
                                                <HostPreview
                                                    hostDataItem={item}
                                                    index={index}
                                                    request_id={request_id}
                                                    requestor_user_id={requestor_user_id}
                                                    game_id={game_id}
                                                    requestor_full_name={requestor_full_name}
                                                    navigation={navigation}
                                                    lastIndex={hostData?.length - 2}
                                                    dividerStyle={{ backgroundColor: colors.whiteRGB }}
                                                />
                                            ))}
                                </View>
                            </>
                        ) : null}
                        <TouchableOpacity style={styles.markCompleteButton} onPress={() => handleMarkComplete(item)}>
                            <Check width={Size.SIZE_12} height={Size.SIZE_10} />
                            <Text style={styles.markCompleteText}>Mark Complete</Text>
                        </TouchableOpacity>
                    </>
                )}

                {type === REQUESTED ? (
                    hostData?.length > 0 ? (
                        hostData.map((item: any, index: number) => (
                            <HostPreview
                                hostDataItem={item}
                                index={index}
                                request_id={request_id}
                                requestor_user_id={requestor_user_id}
                                game_id={game_id}
                                requestor_full_name={requestor_full_name}
                                navigation={navigation}
                                lastIndex={hostData?.length - 1}
                            />
                        ))
                    ) : (
                        <View style={styles.noHostsContainer}>
                            <View style={styles.hostIconWrapper}>
                                <HostIcon />
                            </View>
                            <Text style={styles.noHostsText}>No hosts yet</Text>
                        </View>
                    )
                ) : null}

                {type === RECEIVED && (
                    <>
                        <View style={styles.requestNotesStyle}>
                            <Text style={styles.requestNotesText} numberOfLines={3}>
                                {message.length > 150 ? message.slice(0, 130) + '...' : message}
                                {message.length > 150 && <Text style={styles.readMoreText}> Read more</Text>}
                            </Text>
                        </View>
                        <View style={styles.requesterProfileContainer}>
                            <Pressable
                                style={styles.profileIconWrapper}
                                onPress={() => redirectToUserProfile({ userId: requestor_user_id, navigation, user })}>
                                {requestor_profile_photo ? (
                                    <Image
                                        source={{ uri: requestor_profile_photo }}
                                        style={styles.requesterProfileImage}
                                    />
                                ) : (
                                    <Text style={styles.requesterProfileInitials}>{requestor_full_name[0]}</Text>
                                )}
                            </Pressable>
                            <Text style={styles.requesterProfileText}>
                                {requestor_full_name}
                                {item?.number_of_players > 1 && (
                                    <Text>
                                        {`  `}+{item?.number_of_players - 1}
                                    </Text>
                                )}
                            </Text>
                        </View>
                        <View style={styles.cardButtonWrapper}>
                            <TouchableOpacity
                                style={styles.crossButton}
                                onPress={() => {
                                    navigation.navigate('RequestConfirmScreen', {
                                        game_id: game_id,
                                        requestor_full_name: requestor_full_name,
                                        acceptRequest: false,
                                        chatAgainCallBack: goToChatScreen,
                                        request: item,
                                        declineRequest: true,
                                        type: handleType(),
                                        showReason: true,
                                    });
                                }}>
                                <CircleCrossIcon height={Size.SIZE_16} width={Size.SIZE_16} />
                            </TouchableOpacity>
                            <TealButtonNew
                                btnStyle={[
                                    styles.customStyle,
                                    {
                                        backgroundColor:
                                            requester_has_message !== undefined &&
                                            has_messages !== undefined &&
                                            requester_has_message &&
                                            has_messages
                                                ? colors.greenColor
                                                : colors.lightgray,
                                    },
                                ]}
                                textStyle={[
                                    styles.btnTextStyle,
                                    {
                                        color:
                                            requester_has_message !== undefined &&
                                            has_messages !== undefined &&
                                            requester_has_message &&
                                            has_messages
                                                ? colors.white
                                                : colors.darkgray,
                                    },
                                ]}
                                text="Accept"
                                onPress={handleAcceptRequest}
                                loading={false}
                            />
                            <TouchableOpacity
                                onPress={() => {
                                    // setChannel({});
                                    goToChatScreen();
                                }}
                                style={[
                                    styles.chatButtonStyle,
                                    {
                                        backgroundColor:
                                            has_messages && requester_has_message ? colors.tealRgb : colors.opacityTeal,
                                    },
                                ]}>
                                {has_messages && requester_has_message ? (
                                    <ChatWhiteIcon height={Size.SIZE_16} width={Size.SIZE_16} />
                                ) : (
                                    <ChatTealNewIcon height={Size.SIZE_16} width={Size.SIZE_16} />
                                )}
                                <Text
                                    style={[
                                        styles.chatButtonText,
                                        {
                                            color:
                                                requester_has_message && has_messages
                                                    ? colors.whiteRGB
                                                    : colors.tealRgb,
                                        },
                                    ]}>
                                    Chat
                                </Text>
                            </TouchableOpacity>
                            {unreadChannelsObject?.some((data: any) => data?.data?.request_id === item?.request_id) ? (
                                <View style={styles.tealDotStyle} />
                            ) : null}
                        </View>
                    </>
                )}
                {is_first_request && type === RECEIVED && (
                    <View style={{ position: 'absolute', left: -Spacing.SCALE_4, top: -2 }}>
                        <FirstTimeRequester />
                    </View>
                )}
                {user?.is_tg_founder && type === RECEIVED && (
                    <View style={{ position: 'absolute', left: -Spacing.SCALE_4, top: -2 }}>
                        <IsTgFounderClubMemberIcon />
                    </View>
                )}
            </TouchableOpacity>
        </>
    );
};

export default DynamicRequestItemCard;

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_16,
        borderRadius: Size.SIZE_16,
        borderWidth: 1,
        borderColor: colors.greyRgba,
        backgroundColor: colors.whiteRGB,
        marginVertical: Spacing.SCALE_4,
        alignSelf: 'flex-end',
    },
    headerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    headerLeftContainer: {
        flexDirection: 'row',
        gap: Spacing.SCALE_8,
    },
    headerLeftText: {
        fontSize: Size.SIZE_16,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
        width: Size.SIZE_230,
    },
    iconContainer: {
        width: Size.SIZE_24,
        height: Size.SIZE_24,
        borderRadius: Size.SIZE_12,
        justifyContent: 'center',
        alignItems: 'center',
    },
    clubIcon: {
        fontSize: Size.SIZE_14,
    },
    headerRightContainer: {
        flexDirection: 'row',
        gap: Spacing.SCALE_8,
    },
    clubIdStyle: {
        fontSize: Size.SIZE_11,
        color: colors.fadeBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    dateStyle: {
        fontSize: Size.SIZE_12,
        color: colors.fadeBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    requestNotesStyle: {
        marginTop: Spacing.SCALE_8,
    },
    requestNotesText: {
        fontSize: Size.SIZE_12,
        color: colors.lightBlack,
        lineHeight: Size.SIZE_16,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    readMoreText: {
        color: colors.tealRgb,
        fontSize: Size.SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_4,
    },
    requesterProfileContainer: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_6,
        marginTop: Spacing.SCALE_14,
        alignItems: 'center',
    },
    requesterProfileText: {
        fontSize: Size.SIZE_12,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
    },
    profileIconWrapper: {
        width: Size.SIZE_20,
        height: Size.SIZE_20,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.lightgray,
        justifyContent: 'center',
        alignItems: 'center',
    },
    requesterProfileInitials: {
        fontSize: Size.SIZE_12,
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Regular',
        textTransform: 'uppercase',
    },
    requesterProfileImage: {
        width: Size.SIZE_18,
        height: Size.SIZE_18,
        borderRadius: Size.SIZE_50,
    },
    cardButtonWrapper: {
        flexDirection: 'row',
        marginTop: Spacing.SCALE_14,
        justifyContent: 'space-between',
    },
    crossButton: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.lightgray,
        justifyContent: 'center',
        alignItems: 'center',
    },
    customStyle: {
        width: '40%',
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.greenColor,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 0,
    },
    btnTextStyle: {
        fontSize: Size.SIZE_12,
        lineHeight: Size.SIZE_18,
        color: colors.white,
        fontFamily: 'Ubuntu-Regular',
    },
    chatButtonStyle: {
        width: '40%',
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: colors.tealRgb,
        flexDirection: 'row',
        columnGap: Spacing.SCALE_7,
    },
    chatButtonText: {
        fontSize: Size.SIZE_12,
        lineHeight: Size.SIZE_18,
        fontFamily: 'Ubuntu-Regular',
    },
    tealDotStyle: {
        width: 10,
        height: 10,
        borderRadius: 50,
        backgroundColor: colors.tealRgb,
        position: 'absolute',
        right: -2,
        borderWidth: 1,
        borderColor: colors.whiteRGB,
        top: -2,
    },
    buttonWrapper: {
        width: Size.SIZE_30,
        height: Size.SIZE_28,
        alignItems: 'flex-end',
    },
    noHostsText: {
        fontSize: Size.SIZE_12,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    hostIconWrapper: {
        backgroundColor: colors.requestedBtnColor,
        height: Size.SIZE_32,
        width: Size.SIZE_32,
        borderRadius: Size.SIZE_50,
        alignItems: 'center',
        justifyContent: 'center',
    },
    noHostsContainer: {
        marginTop: Spacing.SCALE_12,
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_8,
    },
    markCompleteButton: {
        backgroundColor: colors.greenColor,
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_7,
        borderRadius: Size.SIZE_8,
        height: Size.SIZE_32,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: Spacing.SCALE_16,
        flexDirection: 'row',
        columnGap: Spacing.SCALE_6,
        borderWidth: 1,
        borderColor: colors.tealRgb,
    },
    markCompleteText: {
        fontSize: Size.SIZE_12,
        color: colors.whiteRGB,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_18,
        fontWeight: '400',
    },
    otherHostsText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        marginBottom: Spacing.SCALE_8,
    },
    acceptingHostsText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        marginTop: Spacing.SCALE_8,
    },
});
