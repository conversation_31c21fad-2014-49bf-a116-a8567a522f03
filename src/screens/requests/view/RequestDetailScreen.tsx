import { StatusBar, StyleSheet, Text, View, ScrollView, TouchableOpacity, Platform } from 'react-native';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useFocusEffect } from '@react-navigation/native';

import { colors } from '../../../theme/theme';
import ProfileHeader from '../../../components/layout/ProfileHeader';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import {
    ClubGolfIconTeal,
    NewCalenderIcon,
    CircleCrossIcon,
    ChatWhiteIcon,
    ChatTealNewIcon,
    EditIconBlack,
} from '../../../assets/svg';
import { getRequestDetails } from '../action/getRequestDetails';
import { GET_REQUESTED_REQUEST_INFO, GET_RECIEVED_REQUEST_INFO } from '../../../service/EndPoint';
import { AuthContext } from '../../../context/AuthContext';
import {
    ACCOMPANIED_PLAY_ONLY,
    GAME_DATE,
    GAME_INFORMATION,
    REQUEST_INFORMATION,
    REQUESTED_DATE,
} from '../../../utils/constants/strings';
import { RequestDetails, RequestedAcceptedRequest } from '../../../interface';
import { RootStackParamList } from '../../../interface/type';
import TealButtonNew from '../../../components/buttons/TealButtonNew';
import { GlobalContext } from '../../../context/contextApi';
import showToast from '../../../components/toast/CustomToast';
import AcceptRequestModalNew from '../../../components/modals/AcceptRequestModalNew';
import { StreamChatContext } from '../../../context/StreamChatContext';
import { getStreamRequestChannel } from '../action/getRequestUnReadChannels';
import RequestScreenSkelton from './RequestScreenSkelton';
import redirectToUserProfile from '../action/openUserProfile';
import { Check } from '../../../assets/images/svg';
import ReviewModalNew from '../../../components/modals/ReviewModalNew';
import RenderRequesterDetail from './RenderRequesterDetail';
import RenderHosts from './RenderHosts';
import RenderAdditionalInfo from './RenderAdditionalInfo';
import { getAllRequestHostsData } from '../action/getAllRequestHostsData';
import CustomRM from '../../../components/layout/requests/customRM';
import { UPDATE_USER } from '../../../graphql/mutations/user';
import useClient from '../../../hooks/useClient';
import { getAllRequest } from '../action/getAllRequest';

interface RequestDetailScreenProps {
    route: {
        params: {
            request: any;
            type: string;
            isMyRequest: boolean;
        };
    };
    navigation: NativeStackNavigationProp<RootStackParamList>;
}

interface AcceptRequestModalState {
    request: any;
    onRefresh: () => void;
}

const RequestDetailScreen: React.FC<RequestDetailScreenProps> = ({ route, navigation }) => {
    const { request, isMyRequest } = route.params;
    const { user } = useContext(AuthContext);
    const { actions, state } = useContext(GlobalContext);
    const apolloClient = useClient();
    const { client, setChannel } = useContext(StreamChatContext);
    const { unreadChannels } = state;
    const [requestDetails, setRequestDetails] = React.useState<RequestDetails | null>(null);
    const [acceptRequestModal, setAcceptRequestModal] = React.useState<AcceptRequestModalState | null>(null);
    const [declineRequest, setDeclineRequest] = React.useState<RequestedAcceptedRequest | null>(null);
    const [reviewModal, setReviewModal] = React.useState<boolean>(false);
    const [type, setType] = useState(route.params.type);
    const [isVisible, setIsVisible] = useState(false);
    const [isAccepted, setIsAccepted] = useState(false);

    const getDetails = useCallback(async () => {
        try {
            actions.setAppSkeltonLoader(true);
            const res = await getRequestDetails(
                type.includes('received') ? GET_RECIEVED_REQUEST_INFO : GET_REQUESTED_REQUEST_INFO,
                {
                    userId: user?.id,
                    requestId: request?.request_id,
                },
            );
            if (res.status) {
                setRequestDetails(res.data);
            } else {
                showToast({});
            }
        } catch (error) {
            showToast({});
        } finally {
            actions.setAppSkeltonLoader(false);
        }
    }, [user?.id, request?.request_id, type, actions]);

    useEffect(() => {
        getDetails();
    }, [getDetails]);

    // Handle request hosts data
    useEffect(() => {
        if (request?.request_id) {
            getAllRequestHostsData({
                userId: user?.id,
                requestIds: [request?.request_id],
            }).then((res) => {
                if (res.status) {
                    actions.setAllRequestHostsData(res.data);
                }
            });
        }
    }, [request, user?.id, actions]);

    const hostData = useMemo(() => {
        return state.allRequestHostsData?.find((data: any) => data.request_id === request.request_id)?.hosts || [];
    }, [request, state.allRequestHostsData]);

    useFocusEffect(
        useCallback(() => {
            const updateData = async () => {
                await Promise.all([getStreamRequestChannel(client, user, actions, state), getDetails()]);
            };
            updateData();
        }, [user?.id, client, actions, getDetails]),
    );

    useEffect(() => {
        if (!client) return;

        const messageNewEventListener = client.on('message.new', async () => {
            await getStreamRequestChannel(client, user, actions, state);
        });

        return () => {
            messageNewEventListener?.unsubscribe();
        };
    }, [client, user, actions]);

    const gameInfo = useMemo(() => requestDetails?.gameInfo, [requestDetails]);
    const userInfo = useMemo(() => requestDetails?.userInfo, [requestDetails]);

    const {
        game_id,
        stream_channel_id,
        request_id,
        requestor_user_id,
        host_user_id,
        has_messages,
        requestor_full_name,
        requester_has_message,
    } = useMemo(() => {
        return {
            game_id: request.game_id,
            stream_channel_id: request.stream_channel_id,
            request_id: request.request_id,
            requestor_user_id: request.requestor_user_id,
            host_user_id: request.host_user_id,
            has_messages: request.has_messages,
            requestor_full_name: request.requestor_full_name,
            requester_has_message: request.requester_has_message,
        };
    }, [request]);

    const goToChatScreen = useCallback(() => {
        setChannel({});
        if (host_user_id && requestor_user_id && host_user_id !== requestor_user_id) {
            navigation.navigate('ChatDetails', {
                type: 'received-open',
                request_id,
                game_id,
                streamChannelId: stream_channel_id,
                requestor_user_id,
                requestor_full_name,
                host_user_id,
                has_messages,
            });
        } else {
            showToast({});
        }
    }, [
        host_user_id,
        requestor_user_id,
        navigation,
        game_id,
        stream_channel_id,
        request_id,
        requestor_full_name,
        has_messages,
    ]);

    const showRequestPopup = useCallback(() => {
        if (user?.additional_settings?.showAcceptRequestPopup) {
            setIsVisible(true);
        } else {
            navigation.navigate('RequestConfirmScreen', {
                game_id,
                requestor_full_name,
                acceptRequest: true,
                chatAgainCallBack: goToChatScreen,
                request,
                type: type,
                handleYesButton: () => {
                    setAcceptRequestModal({
                        request,
                        onRefresh: getDetails,
                    });
                },
            });
        }
    }, [
        user?.additional_settings?.showAcceptRequestPopup,
        navigation,
        game_id,
        requestor_full_name,
        request,
        goToChatScreen,
    ]);

    const handleEditRequest = useCallback(() => {
        navigation.navigate('Create Request', { request });
    }, [navigation, request]);

    const handleMarkComplete = () => {
        setDeclineRequest(request);
        setReviewModal(true);
    };

    const handleDelete = (item: any) => {
        navigation.navigate('RequestConfirmScreen', {
            type: 'history',
            game_id: item.game_id,
            requestor_full_name: item.requestor_full_name,
            request: item,
            callBack: () => {
                navigation.goBack();
            },
        });
    };

    const handleModalClose = useCallback(() => {
        setIsVisible(false);
    }, []);

    const handleContinue = useCallback(() => {
        setIsVisible(false);

        if (isAccepted) {
            apolloClient.request(UPDATE_USER, {
                user_id: user?.id,
                user: {
                    additional_settings: {
                        showAcceptRequestPopup: false,
                        showCreateRequestPopup: user?.additional_settings?.showCreateRequestPopup,
                    },
                },
            });
        }

        setTimeout(() => {
            navigation.navigate('RequestConfirmScreen', {
                game_id: game_id,
                requestor_full_name: requestor_full_name,
                acceptRequest: true,
                type: type,
                chatAgainCallBack: goToChatScreen,
                request: request,
                handleYesButton: () => {
                    setAcceptRequestModal &&
                        setAcceptRequestModal({
                            request: request,
                            onRefresh: () => getAllRequest(actions, user?.id),
                        });
                },
            });
        }, 100);
    }, [isAccepted, apolloClient, user?.id]);

    const handleCrossIcon = () => {
        if (
            type === 'received-accepted' ||
            type === 'received-open' ||
            type === 'requested-accepted' ||
            type === 'requested-open'
        ) {
            navigation.navigate('RequestConfirmScreen', {
                game_id: game_id,
                requestor_full_name: requestor_full_name,
                declineRequest: true,
                request_id: request_id,
                type: type,
                request: request,
                showReason: true,
                callBack: () => navigation.goBack(),
                hostData: hostData,
            });
        } else {
            navigation.navigate('RequestConfirmScreen', {
                game_id,
                requestor_full_name,
                acceptRequest: false,
                chatAgainCallBack: goToChatScreen,
                request,
                deleteRequest: true,
                callBack: () => navigation.goBack(),
                request_id: request.request_id,
                type: type,
            });
        }
    };

    const renderGameInformation = () => (
        <>
            <Text style={styles.title}>{request?.game_date ? GAME_INFORMATION : REQUEST_INFORMATION}</Text>
            <View style={styles.box}>
                <View style={styles.infoRow}>
                    <ClubGolfIconTeal width={20} height={20} />
                    <View style={styles.infoContent}>
                        <Text style={styles.label}>Club Name</Text>
                        <Text style={styles.value}>{gameInfo?.club_name || ''}</Text>
                    </View>
                </View>
                <View style={styles.infoRow}>
                    <NewCalenderIcon width={20} height={20} />
                    <View style={styles.infoContent}>
                        <Text style={styles.label}>{request?.game_date ? GAME_DATE : REQUESTED_DATE}</Text>
                        <Text style={styles.dateStyle}>{gameInfo?.date || ''}</Text>
                    </View>
                </View>
                <Text style={styles.message}>{gameInfo?.message || ''}</Text>
                {gameInfo?.accompanied_only && <Text style={styles.accompaniedText}>{ACCOMPANIED_PLAY_ONLY}</Text>}
            </View>
        </>
    );

    const renderBottomActions = () => (
        <View style={styles.bottomBox}>
            <View style={styles.cardButtonWrapper}>
                {!type.includes('history') && (
                    <TouchableOpacity
                        style={[
                            styles.crossButton,
                            { opacity: request?.host_completed || request?.requestor_completed ? 0.3 : 1 },
                        ]}
                        disabled={request?.host_completed || request?.requestor_completed}
                        onPress={handleCrossIcon}>
                        <CircleCrossIcon height={Size.SIZE_18} width={Size.SIZE_18} />
                    </TouchableOpacity>
                )}
                {type.includes('history') && (
                    <TouchableOpacity style={styles.chatButton} onPress={goToChatScreen}>
                        <ChatWhiteIcon height={Size.SIZE_16} width={Size.SIZE_16} />
                        <Text style={[styles.chatButtonText, { color: colors.whiteRGB }]}>Chat</Text>
                    </TouchableOpacity>
                )}
                {(type === 'requested-accepted' || type === 'received-accepted') && (
                    <TouchableOpacity
                        style={[styles.editButtonWrapper, { backgroundColor: colors.greenColor }]}
                        onPress={handleMarkComplete}>
                        <Check />
                        <Text style={[styles.editTextStyle, { color: colors.whiteRGB }]}>Mark Complete</Text>
                    </TouchableOpacity>
                )}
                {type === 'received-open' && (
                    <>
                        <TealButtonNew
                            btnStyle={[
                                styles.customStyle,
                                {
                                    backgroundColor:
                                        requester_has_message && has_messages ? colors.greenColor : colors.lightgray,
                                },
                            ]}
                            textStyle={[
                                styles.btnTextStyle,
                                {
                                    color: requester_has_message && has_messages ? colors.white : colors.darkgray,
                                },
                            ]}
                            text="Accept"
                            onPress={() => {
                                if (has_messages && requester_has_message) {
                                    showRequestPopup();
                                } else {
                                    navigation.navigate('RequestConfirmScreen', {
                                        game_id,
                                        requestor_full_name,
                                        acceptRequest: false,
                                        chatAgainCallBack: goToChatScreen,
                                        request,
                                        handleYesButton: () => {},
                                        type: type,
                                    });
                                }
                            }}
                            loading={false}
                        />
                        <TouchableOpacity
                            onPress={goToChatScreen}
                            style={[
                                styles.chatButtonStyle,
                                {
                                    backgroundColor:
                                        has_messages && requester_has_message ? colors.tealRgb : colors.opacityTeal,
                                },
                            ]}>
                            {has_messages && requester_has_message ? (
                                <ChatWhiteIcon height={Size.SIZE_16} width={Size.SIZE_16} />
                            ) : (
                                <ChatTealNewIcon height={Size.SIZE_16} width={Size.SIZE_16} />
                            )}
                            <Text
                                style={[
                                    styles.chatButtonText,
                                    {
                                        color: requester_has_message && has_messages ? colors.whiteRGB : colors.tealRgb,
                                    },
                                ]}>
                                Chat
                            </Text>
                        </TouchableOpacity>
                    </>
                )}
                {type === 'requested-open' && (
                    <TouchableOpacity style={styles.editButtonWrapper} onPress={handleEditRequest}>
                        <EditIconBlack />
                        <Text style={styles.editTextStyle}>Edit</Text>
                    </TouchableOpacity>
                )}
                {type === 'received-open' &&
                unreadChannels?.filter((data: any) => data === request?.request_id)?.length ? (
                    <View style={styles.tealDotStyle} />
                ) : null}
            </View>
        </View>
    );

    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor={colors.whiteRGB} />
            <View style={styles.container}>
                <ProfileHeader
                    title={`Request #${gameInfo?.game_id || ''}`}
                    onClick={() => handleDelete(request)}
                    headerTitleStyle={styles.headerTitleStyle}
                    backButtonFillColor={colors.lightBlack}
                    containerStyle={{
                        backgroundColor: colors.whiteRGB,
                        paddingBottom: Spacing.SCALE_10,
                    }}
                    iconShow={type}
                />
                <ScrollView style={[styles.body, { marginBottom: type === 'requested-history' ? 0 : Size.SIZE_64 }]}>
                    {state.appSkeltonLoader ? (
                        <RequestScreenSkelton screen="RequestDetail" />
                    ) : (
                        <>
                            {renderGameInformation()}
                            {(type === 'received-open' ||
                                type === 'requested-accepted' ||
                                type === 'received-accepted' ||
                                type.includes('received-history')) &&
                                !isMyRequest && (
                                    <RenderRequesterDetail
                                        request={request}
                                        userInfo={userInfo}
                                        redirectToUserProfile={redirectToUserProfile}
                                        navigation={navigation}
                                        user={user}
                                        type={type}
                                    />
                                )}
                            {(type === 'requested-open' ||
                                type === 'requested-accepted' ||
                                type.toLowerCase().includes('requested-history')) && (
                                <RenderHosts
                                    hostData={hostData}
                                    type={type}
                                    navigation={navigation}
                                    unreadChannels={unreadChannels}
                                    request_id={request_id}
                                    requestor_user_id={requestor_user_id}
                                    game_id={game_id}
                                    requestor_full_name={requestor_full_name}
                                    userInfo={userInfo}
                                    request={request}
                                />
                            )}

                            {request?.request_for_all &&
                                !request?.offer_details?.id &&
                                type.toLowerCase() === 'requested-open' && (
                                    <RenderAdditionalInfo
                                        hostData={hostData}
                                        type={type}
                                        request={request}
                                        gameInfo={gameInfo}
                                        isMyRequest={isMyRequest}
                                        userInfo={userInfo}
                                    />
                                )}

                            {type.toLowerCase() !== 'requested-open' &&
                                type.toLowerCase() !== 'received-open' &&
                                type !== 'received-accepted' &&
                                type !== 'received-history' &&
                                !request?.offer_details?.id &&
                                !userInfo?.deleted_at && (
                                    <RenderAdditionalInfo
                                        hostData={hostData}
                                        type={type}
                                        request={request}
                                        gameInfo={gameInfo}
                                        isMyRequest={isMyRequest}
                                        userInfo={userInfo}
                                    />
                                )}
                        </>
                    )}
                </ScrollView>
            </View>
            {!state.appSkeltonLoader && type !== 'requested-history' && renderBottomActions()}
            {acceptRequestModal && (
                <AcceptRequestModalNew
                    modal={acceptRequestModal}
                    setModal={setAcceptRequestModal}
                    callBack={() => {
                        setType('received-accepted');
                    }}
                />
            )}
            {reviewModal && (
                <ReviewModalNew
                    request={declineRequest}
                    isMyRequest={isMyRequest}
                    closeModal={() => {
                        setReviewModal(false);
                        setDeclineRequest(null);
                        navigation.goBack();
                    }}
                />
            )}
            {user?.additional_settings?.showAcceptRequestPopup && (
                <CustomRM
                    isVisible={isVisible}
                    label={'Request'}
                    heading="Are you sure you want to accept the request?"
                    data={[
                        `This is a no-obligation system.  You don't have to accept anything that doesn't work for you`,
                        `Clarify that logistics work before accepting`,
                        `Learn enough about the requester so that you are sure you want to spend a few hours with them`,
                        `We are not big fans of "unaccompanied" play.  Accept if you can host.  If setting up unaccompanied play, remember that you are responsible for guest conduct`,
                    ]}
                    isChecked={isAccepted}
                    handleChecked={() => setIsAccepted(!isAccepted)}
                    handleContinue={handleContinue}
                    handleCancel={handleModalClose}
                    handleBack={handleModalClose}
                />
            )}
        </>
    );
};

export default React.memo(RequestDetailScreen);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.whiteRGB,
        ...Platform.select({
            ios: {
                shadowColor: '#000',
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
            },
            android: {
                elevation: 5,
            },
        }),
    },
    headerTitleStyle: {
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_1,
    },
    body: {
        flex: 1,
        paddingHorizontal: Spacing.SCALE_16,
        paddingVertical: Spacing.SCALE_12,
        marginBottom: Size.SIZE_80,
        backgroundColor: colors.screenBG,
    },
    title: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
    },
    box: {
        backgroundColor: colors.whiteRGB,
        paddingVertical: Spacing.SCALE_16,
        paddingHorizontal: Spacing.SCALE_12,
        borderRadius: Spacing.SCALE_12,
        marginTop: Spacing.SCALE_8,
        marginBottom: Spacing.SCALE_16,
    },
    infoRow: {
        flexDirection: 'row',
        marginBottom: Spacing.SCALE_16,
    },
    infoContent: {
        marginLeft: Spacing.SCALE_8,
        rowGap: Spacing.SCALE_4,
        flex: 1,
    },
    label: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.darkgray,
        fontFamily: 'Ubuntu-Regular',
    },
    value: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
    },
    divider: {
        height: 1,
        backgroundColor: colors.lightgray,
        marginVertical: Spacing.SCALE_8,
    },
    message: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_16,
    },
    accompaniedText: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_22,
        marginTop: Spacing.SCALE_16,
    },
    dateStyle: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    ftrTextContainer: {
        flexDirection: 'row',
        backgroundColor: 'rgba(9, 128, 137, 0.07)',
        paddingHorizontal: Spacing.SCALE_10,
        paddingVertical: Spacing.SCALE_12,
        borderRadius: Size.SIZE_8,
        columnGap: Spacing.SCALE_10,
    },
    MainTextStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '500',
        color: colors.lightBlack,
        lineHeight: Typography.FONT_SIZE_16,
        width: Size.SIZE_240,
    },
    badgeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: Spacing.SCALE_8,
        flexWrap: 'wrap',
        gap: Spacing.SCALE_8,
        marginTop: Spacing.SCALE_10,
    },
    badge: {
        paddingVertical: Spacing.SCALE_5,
        paddingHorizontal: Spacing.SCALE_8,
        borderRadius: Size.SIZE_6,
    },
    founderBadge: {
        backgroundColor: colors.opacityTeal,
    },
    superHostBadge: {
        backgroundColor: colors.badgeLabelBackgroundColor,
        borderColor: colors.darkteal,
    },
    badgeText: {
        fontSize: Typography.FONT_SIZE_10,
        color: colors.darkteal,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Typography.FONT_SIZE_10,
    },
    userInfoContainer: {
        marginTop: Spacing.SCALE_16,
        flexDirection: 'row',
        backgroundColor: colors.lightgray,
        borderRadius: Size.SIZE_12,
        padding: Spacing.SCALE_8,
        alignItems: 'center',
        columnGap: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_6,
    },
    profileImageContainer: {
        width: Size.SIZE_38,
        height: Size.SIZE_38,
        alignItems: 'center',
        justifyContent: 'center',
    },
    profileImage: {
        width: '100%',
        height: '100%',
        borderRadius: Size.SIZE_70 / 2,
        backgroundColor: colors.tealRgb,
    },
    profileImageFallback: {
        width: '100%',
        height: '100%',
        borderRadius: Size.SIZE_70 / 2,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
    },
    profileImageText: {
        fontSize: Typography.FONT_SIZE_24,
        color: colors.whiteRGB,
        fontFamily: 'Ubuntu-Medium',
    },
    userDetailsContainer: {
        flex: 1,
        marginLeft: Spacing.SCALE_16,
    },
    userName: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
    },
    contactInfo: {
        marginTop: Spacing.SCALE_4,
    },
    contactRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: Spacing.SCALE_8,
    },
    contactText: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    iconContainer: {
        width: Size.SIZE_16,
        height: Size.SIZE_16,
        justifyContent: 'center',
        alignItems: 'center',
    },
    iconText: {
        fontSize: Typography.FONT_SIZE_12,
    },
    iconWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_5,
        marginTop: Spacing.SCALE_6,
    },
    additionalInfoValue: {
        fontSize: Size.SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_8,
        marginBottom: Spacing.SCALE_12,
    },
    crossButton: {
        height: '100%',
        borderRadius: Size.SIZE_8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    chatButton: {
        width: '100%',
        height: Size.SIZE_40,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 0,
        borderWidth: 1,
        borderColor: colors.tealRgb,
        flexDirection: 'row',
        columnGap: Spacing.SCALE_6,
    },
    customStyle: {
        width: '42%',
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.greenColor,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 0,
    },
    btnTextStyle: {
        fontSize: Size.SIZE_12,
        lineHeight: Size.SIZE_18,
        color: colors.white,
        fontFamily: 'Ubuntu-Regular',
    },
    editTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_18,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    chatButtonStyle: {
        width: '42%',
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: colors.tealRgb,
        flexDirection: 'row',
        columnGap: Spacing.SCALE_7,
    },
    chatButtonText: {
        fontSize: Size.SIZE_12,
        lineHeight: Size.SIZE_18,
        fontFamily: 'Ubuntu-Regular',
    },
    cardButtonWrapper: {
        flexDirection: 'row',
        marginTop: Spacing.SCALE_8,
        justifyContent: 'space-between',
    },
    tealDotStyle: {
        width: 10,
        height: 10,
        borderRadius: 50,
        backgroundColor: colors.tealRgb,
        position: 'absolute',
        right: -2,
        borderWidth: 1,
        borderColor: colors.whiteRGB,
        top: -2,
    },
    bottomBox: {
        position: 'absolute',
        bottom: Spacing.SCALE_1,
        height: Size.SIZE_64,
        backgroundColor: colors.whiteRGB,
        width: '100%',
        paddingHorizontal: Spacing.SCALE_16,
        ...Platform.select({
            ios: {
                shadowColor: colors.shadowColor,
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 4,
            },
            android: {
                elevation: 10,
                shadowOffset: {
                    width: 0,
                    height: 5,
                },
                shadowColor: colors.shadowColor,
                shadowRadius: 4,
                shadowOpacity: 0.5,
            },
        }),
    },
    ambassadorIconContainer: {
        position: 'absolute',
        top: -4,
        right: -4,
        borderRadius: 50,
        padding: 2,
        backgroundColor: colors.whiteRGB,
        borderWidth: 1,
        borderColor: colors.lightgray,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    noHostsText: {
        fontSize: Size.SIZE_12,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    hostIconWrapper: {
        backgroundColor: colors.requestedBtnColor,
        height: Size.SIZE_32,
        width: Size.SIZE_32,
        borderRadius: Size.SIZE_50,
        alignItems: 'center',
        justifyContent: 'center',
    },
    editButtonWrapper: {
        width: '90%',
        height: Size.SIZE_40,
        backgroundColor: colors.lightgray,
        borderRadius: Size.SIZE_8,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        columnGap: Spacing.SCALE_8,
    },
});
