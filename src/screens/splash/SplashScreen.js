import React, { useEffect } from 'react';
import { Image, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from '@react-native-firebase/auth';

import SPLASH from '../../assets/images/splash.jpg';

export default function SplashScreen() {
    useEffect(() => {
        const checkFirstLaunch = async () => {
            if (Platform.OS === 'ios') {
                const hasLaunched = await AsyncStorage.getItem('hasLaunchedIOS');
                if (!hasLaunched) {
                    try {
                        await auth().signOut();
                    } catch (error) {
                        console.error('Error signing out on iOS first launch:', error);
                    }
                    await AsyncStorage.setItem('hasLaunchedIOS', 'true');
                }
            }
        };

        checkFirstLaunch();
    }, []);

    return (
        <Image
            source={SPLASH}
            style={{
                width: '100%',
                height: '100%',
                resizeMode: 'cover',
            }}
        />
    );
}
