import { AppState, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import RNRestart from 'react-native-restart';

class AppStateManager {
    constructor() {
        this.backgroundStartTime = null;
        this.isInBackground = false;
        // TESTING: 1 minute for easy testing - change back to 2 hours for production
        this.backgroundThreshold = 1 * 60 * 1000; // 1 minute in milliseconds
        this.forceReloadThreshold = 1 * 60 * 1000; // 1 minute in milliseconds (force reload)
        this.listeners = [];
        this.appState = AppState.currentState;
    }

    // Initialize the AppState manager
    init() {
        this.setupAppStateListener();
        this.loadBackgroundTime();
    }

    // Setup AppState listener
    setupAppStateListener() {
        const subscription = AppState.addEventListener('change', (nextAppState) => {
            console.log('AppStateManager: App state changed from', this.appState, 'to', nextAppState);
            
            if (this.appState.match(/inactive|background/) && nextAppState === 'active') {
                this.handleAppForeground();
            } else if (nextAppState.match(/inactive|background/)) {
                this.handleAppBackground();
            }
            
            this.appState = nextAppState;
        });

        // Store subscription for cleanup
        this.subscription = subscription;
    }

    // Handle app going to background
    handleAppBackground() {
        console.log('AppStateManager: App going to background');
        this.isInBackground = true;
        this.backgroundStartTime = Date.now();
        this.saveBackgroundTime();
    }

    // Handle app coming to foreground
    async handleAppForeground() {
        console.log('AppStateManager: App coming to foreground');

        if (!this.isInBackground || !this.backgroundStartTime) {
            this.isInBackground = false;
            return;
        }

        const backgroundDuration = Date.now() - this.backgroundStartTime;
        const backgroundSeconds = Math.round(backgroundDuration / 1000); // Convert to seconds
        console.log('AppStateManager: Background duration:', backgroundDuration, 'ms (', backgroundSeconds, 'seconds)');

        // TESTING: Check if app was in background for more than 1 minute - automatically restart
        if (backgroundDuration > this.forceReloadThreshold) {
            console.log('AppStateManager: App was in background for more than 1 minute, automatically restarting app');
            await this.performAutomaticRestart();
        }

        this.isInBackground = false;
        this.backgroundStartTime = null;
        this.clearBackgroundTime();
    }

    // Perform automatic restart without user interaction
    async performAutomaticRestart() {
        try {
            console.log('AppStateManager: Starting automatic app restart...');

            // Clear critical cache data
            await this.clearCriticalCache();

            // Small delay to ensure cache clearing is complete
            setTimeout(() => {
                console.log('AppStateManager: Restarting app now...');
                RNRestart.Restart();
            }, 500);

        } catch (error) {
            console.error('AppStateManager: Error during automatic restart:', error);
            // Fallback to RNRestart if cache clearing fails
            RNRestart.Restart();
        }
    }

    // Legacy method - no longer used for automatic restart
    // Keeping for backward compatibility if needed elsewhere
    suggestAppReload() {
        console.log('AppStateManager: suggestAppReload called but automatic restart is enabled');
        // No longer shows popup - automatic restart handles this
    }

    // Clear critical cache data
    async clearCriticalCache() {
        try {
            const keysToClear = [
                'navigation_state',
                'apollo_cache',
                'stream_chat_state',
                'temp_data'
            ];

            for (const key of keysToClear) {
                await AsyncStorage.removeItem(key);
            }

            console.log('AppStateManager: Critical cache cleared');
        } catch (error) {
            console.error('AppStateManager: Error clearing cache:', error);
        }
    }

    // Trigger app reload
    triggerAppReload() {
        // This will be handled by the main App component
        this.notifyListeners('forceReload');
    }

    // Save background start time
    async saveBackgroundTime() {
        try {
            await AsyncStorage.setItem('background_start_time', this.backgroundStartTime.toString());
        } catch (error) {
            console.error('AppStateManager: Error saving background time:', error);
        }
    }

    // Load background start time
    async loadBackgroundTime() {
        try {
            const savedTime = await AsyncStorage.getItem('background_start_time');
            if (savedTime) {
                this.backgroundStartTime = parseInt(savedTime);
                this.isInBackground = true;
            }
        } catch (error) {
            console.error('AppStateManager: Error loading background time:', error);
        }
    }

    // Clear background time
    async clearBackgroundTime() {
        try {
            await AsyncStorage.removeItem('background_start_time');
        } catch (error) {
            console.error('AppStateManager: Error clearing background time:', error);
        }
    }

    // Add listener for app state events
    addListener(callback) {
        this.listeners.push(callback);
        return () => {
            this.listeners = this.listeners.filter(listener => listener !== callback);
        };
    }

    // Notify all listeners
    notifyListeners(event, data = null) {
        this.listeners.forEach(listener => {
            try {
                listener(event, data);
            } catch (error) {
                console.error('AppStateManager: Error in listener:', error);
            }
        });
    }

    // Get current background duration
    getBackgroundDuration() {
        if (!this.isInBackground || !this.backgroundStartTime) {
            return 0;
        }
        return Date.now() - this.backgroundStartTime;
    }

    // Check if app should reload
    shouldReload() {
        const duration = this.getBackgroundDuration();
        return duration > this.forceReloadThreshold;
    }

    // Cleanup
    cleanup() {
        if (this.subscription) {
            this.subscription.remove();
        }
        this.listeners = [];
    }
}

// Create singleton instance
const appStateManager = new AppStateManager();

export default appStateManager; 