import { AppState, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import RNRestart from 'react-native-restart';

class AppStateManagerTest {
    constructor() {
        this.backgroundStartTime = null;
        this.isInBackground = false;
        // TEST MODE: 30 seconds for easy testing
        this.backgroundThreshold = 30 * 1000; // 30 seconds in milliseconds
        this.forceReloadThreshold = 30 * 1000; // 30 seconds in milliseconds (force reload)
        this.listeners = [];
        this.appState = AppState.currentState;
    }

    // Initialize the AppState manager
    init() {
        this.setupAppStateListener();
        this.loadBackgroundTime();
    }

    // Setup AppState listener
    setupAppStateListener() {
        const subscription = AppState.addEventListener('change', (nextAppState) => {
            console.log('AppStateManagerTest: App state changed from', this.appState, 'to', nextAppState);
            
            if (this.appState.match(/inactive|background/) && nextAppState === 'active') {
                this.handleAppForeground();
            } else if (nextAppState.match(/inactive|background/)) {
                this.handleAppBackground();
            }
            
            this.appState = nextAppState;
        });

        // Store subscription for cleanup
        this.subscription = subscription;
    }

    // Handle app going to background
    handleAppBackground() {
        console.log('AppStateManagerTest: App going to background');
        this.isInBackground = true;
        this.backgroundStartTime = Date.now();
        this.saveBackgroundTime();
    }

    // Handle app coming to foreground
    async handleAppForeground() {
        console.log('AppStateManagerTest: App coming to foreground');
        
        if (!this.isInBackground || !this.backgroundStartTime) {
            this.isInBackground = false;
            return;
        }

        const backgroundDuration = Date.now() - this.backgroundStartTime;
        const backgroundSeconds = Math.round(backgroundDuration / 1000);
        console.log('AppStateManagerTest: Background duration:', backgroundDuration, 'ms (', backgroundSeconds, 'seconds)');

        // Check if app was in background for more than 30 seconds - automatically restart
        if (backgroundDuration > this.forceReloadThreshold) {
            console.log('AppStateManagerTest: App was in background for more than 30 seconds, automatically restarting app');
            await this.performAutomaticRestart();
        }

        this.isInBackground = false;
        this.backgroundStartTime = null;
        this.clearBackgroundTime();
    }

    // Perform automatic restart without user interaction
    async performAutomaticRestart() {
        try {
            console.log('AppStateManagerTest: Starting automatic app restart...');
            
            // Clear critical cache data
            await this.clearCriticalCache();
            
            // Small delay to ensure cache clearing is complete
            setTimeout(() => {
                console.log('AppStateManagerTest: Restarting app now...');
                RNRestart.Restart();
            }, 500);
            
        } catch (error) {
            console.error('AppStateManagerTest: Error during automatic restart:', error);
            // Fallback to RNRestart if cache clearing fails
            RNRestart.Restart();
        }
    }

    // Clear critical cache data
    async clearCriticalCache() {
        try {
            const keysToClear = [
                'navigation_state',
                'apollo_cache',
                'stream_chat_state',
                'temp_data'
            ];

            for (const key of keysToClear) {
                try {
                    await AsyncStorage.removeItem(key);
                    console.log('AppStateManagerTest: Cleared cache key:', key);
                } catch (error) {
                    console.warn('AppStateManagerTest: Could not clear cache key:', key, error);
                }
            }
        } catch (error) {
            console.error('AppStateManagerTest: Error clearing critical cache:', error);
        }
    }

    // Add listener for app state events
    addListener(callback) {
        this.listeners.push(callback);
        
        // Return function to remove listener
        return () => {
            this.listeners = this.listeners.filter(listener => listener !== callback);
        };
    }

    // Notify all listeners
    notifyListeners(event, data = null) {
        this.listeners.forEach(listener => {
            try {
                listener(event, data);
            } catch (error) {
                console.error('AppStateManagerTest: Error in listener:', error);
            }
        });
    }

    // Save background start time
    async saveBackgroundTime() {
        try {
            await AsyncStorage.setItem('background_start_time_test', this.backgroundStartTime.toString());
        } catch (error) {
            console.error('AppStateManagerTest: Error saving background time:', error);
        }
    }

    // Load background start time
    async loadBackgroundTime() {
        try {
            const savedTime = await AsyncStorage.getItem('background_start_time_test');
            if (savedTime) {
                this.backgroundStartTime = parseInt(savedTime);
                this.isInBackground = true;
            }
        } catch (error) {
            console.error('AppStateManagerTest: Error loading background time:', error);
        }
    }

    // Clear background start time
    async clearBackgroundTime() {
        try {
            await AsyncStorage.removeItem('background_start_time_test');
        } catch (error) {
            console.error('AppStateManagerTest: Error clearing background time:', error);
        }
    }

    // Get current background duration
    getBackgroundDuration() {
        if (!this.isInBackground || !this.backgroundStartTime) {
            return 0;
        }
        return Date.now() - this.backgroundStartTime;
    }

    // Check if app should reload
    shouldReload() {
        const duration = this.getBackgroundDuration();
        return duration > this.forceReloadThreshold;
    }

    // Cleanup
    cleanup() {
        if (this.subscription) {
            this.subscription.remove();
        }
        this.listeners = [];
    }
}

// Create singleton instance for testing
const appStateManagerTest = new AppStateManagerTest();

export default appStateManagerTest;
