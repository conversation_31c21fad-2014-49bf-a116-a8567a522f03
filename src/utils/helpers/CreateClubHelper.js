import moment from "moment";
import Moment from "moment";

export function calculateGuestTimeRestrictionDates(closurePeriods, guest_time_availability, month) {
    const guestTimeAvail = Object.keys(guest_time_availability);
    let guestTimeRestrictionDates = [], restrictedDays = [];
    const daysOfTheWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

    if (guestTimeAvail.length < 7) {
        restrictedDays = daysOfTheWeek.filter(day => !guestTimeAvail.includes(day));
        guestTimeRestrictionDates = guestTimeRestrictionDates.concat(getDates(restrictedDays, month));
    }

    if (closurePeriods && closurePeriods.length > 0) {
        const closurePeriodDates = findDatesFromDateRange(closurePeriods)
        guestTimeRestrictionDates = guestTimeRestrictionDates.concat(closurePeriodDates);
    }

    return guestTimeRestrictionDates;
}

export function findDatesFromDateRange(dateRange) {
    return dateRange.map(({ from, to }) => {
        const startDate = Moment(from, 'MM-DD-YYYY')
        const endDate = Moment(to, 'MM-DD-YYYY');
        
        // Check if dates are valid
        if (!startDate.isValid() || !endDate.isValid()) {
            console.warn('Invalid date in date range:', { from, to });
            return [];
        }
        
        const nDaysBetween = endDate.diff(startDate, 'd');
        
        // Prevent creating arrays that are too large
        if (nDaysBetween > 365) { // Limit to 1 year maximum
            console.warn('Date range too large, limiting to 365 days:', { from, to });
            return [];
        }
        
        try {
            const dates = new Array(nDaysBetween + 1)
                .fill('')
                .map((_, index) => {
                    return Moment(from)
                        .add(index, 'days')
                        .format('YYYY-MM-DD');
                });
            console.log('dates', dates, nDaysBetween);
            return dates;
        } catch (error) {
            console.warn('Error creating date range array:', error);
            return [];
        }
    }).flat();

}

function getDates(restrictedDays, month) {
    let dates = [];
    var dateTo = month ? moment().add(month, 'months').format('YYYY-MM-DD') : Moment().add(90, 'd').format('YYYY-MM-DD');

    restrictedDays.map(restrictedDay => {
        var calcDay = Moment().day(restrictedDay);
        while (dateTo >= calcDay.format('YYYY-MM-DD')) {
            dates.push(calcDay.format('YYYY-MM-DD'));
            calcDay.add(7, 'd');
        }
    })
    return dates;
}

const DefaultFilters = {
    pace: ["fast", "average", "leisure"],
    gender: "both",
    all_ages: true,
    max_age: null,
    min_age: null,
    handicap: ["< 5", "5-10", "> 10"],
    playAsCouple: false,
    englishFluency: ["native", "fluent", "understandable", "basic"],
    sendToPrivateNetwork: null
};

// export function getClubMembersForUser({ user_data, user_array, filters = DefaultFilters }) {

//     let {
//         pace = ["fast", "average", "leisure"],
//         gender = "both",
//         all_ages = true,
//         max_age = null,
//         min_age = null,
//         handicap = ["< 5", "5-10", "> 10"],
//         playAsCouple = false,
//         englishFluency = ["native", "fluent", "understandable", "basic"],
//         sendToPrivateNetwork = null
//     } = filters;


//     const { tier: requesterTier, private_network: requesterPrivateNetwork, visibleToPublic: requesterVisibleToPublic } = user_data;
//     const userClubIds = user_data.clubs.map(({ club }) => club?.id || club?.club_id)

//     let requesterPrivateNetworkId = null;
//     if (requesterPrivateNetwork && requesterPrivateNetwork.hasOwnProperty('id')) {
//         requesterPrivateNetworkId = requesterPrivateNetwork.id
//     } else if (requesterPrivateNetwork && requesterPrivateNetwork.hasOwnProperty('private_network_id')) {
//         requesterPrivateNetworkId = requesterPrivateNetwork.private_network_id
//     }

//     const response = user_array.filter(user => {

//         if (user.visible_to_favorite_clubs) {
//             user.favourite_clubs
//             const filteredClubs = user.favourite_clubs.filter(club => club && userClubIds.includes(club))

//             if (!filteredClubs.length) {
//                 return false;
//             }
//         }

//         if ((user.pace && !pace.includes((user.pace).toLowerCase())) || !user.pace) {
//             return false;
//         }
//         if ((user.gender && gender !== 'both' && (user.gender).toLowerCase() !== gender) || !user.gender) {
//             return false;
//         }
//         if (!all_ages && (user.age < min_age || user.age > max_age)) {
//             return false;
//         }
//         if ((user.handicap && !handicap.includes(user.handicap)) || !user.handicap) {
//             return false;
//         }
//         if (playAsCouple && playAsCouple !== user.playAsCouple) {
//             return false;
//         }
//         if ((user.englishFluency && !englishFluency.includes((user.englishFluency).toLowerCase())) || !user.englishFluency) {
//             return false;
//         }

//         // ********* Checking on the basis of TG, PN or TG + PN members **********
//         if (requesterPrivateNetworkId && !requesterVisibleToPublic &&
//             requesterPrivateNetworkId !== user.private_network) {
//             /* 
//             * User is only a member of Private Network
//             * No consideration of tier is required in this case
//             */
//             return false;
//         } else {
//             /* 
//             * User is a member of TG and/or Private Network
//             */
//             const hostTier = calculateLowestTier(user.tier, user.visibleToLowerTiers, user.tierVisibility);

//             if (sendToPrivateNetwork) {
//                 /* 
//                 * User is willing to send request to private network only
//                 */

//                 if (user.private_network && requesterPrivateNetworkId === user.private_network) {
//                     return true;
//                 } else {
//                     return false;
//                 }
//             } else {
//                 /* 
//                 * User is willing to send request to both private network and TG members
//                 */

//                 if (requesterPrivateNetworkId && requesterVisibleToPublic) {
//                     if ((user.private_network && requesterPrivateNetworkId === user.private_network) ||
//                         (user.visibleToPublic && hostTier >= requesterTier)
//                     ) {
//                         return true;
//                     }
//                     if (!user.private_network && hostTier >= requesterTier) {
//                         // To check if the user's tier is greater than the requester's tier and user is not a PN member
//                         return true;
//                     }
//                     return false;
//                 } else if (!requesterPrivateNetworkId) {

//                     if (user.private_network && !user.visibleToPublic) {
//                         // User should not be a member of PN alone
//                         return false;
//                     } else {
//                         if (hostTier >= requesterTier) {
//                             return true;
//                         }
//                         return false;
//                     }
//                 }
//             }
//         }

//         return true;

//     })

//     return response;
// }
export function getClubMembersForUser({ user_data, user_array, filters = DefaultFilters, club_id = null, clubTier }) {
    let {
        pace = ["fast", "average", "leisure"],
        gender = "both",
        all_ages = true,
        max_age = null,
        min_age = null,
        handicap = ["< 5", "5-10", "> 10"],
        playAsCouple = false,
        englishFluency = ["native", "fluent", "understandable", "basic"],
        sendToPrivateNetwork = null
    } = filters;
    
    const { tier: requesterTier, private_network: requesterPrivateNetwork, visibleToPublic: requesterVisibleToPublic } = user_data;
    const userClubIds = user_data.clubs.map(club => club.id || club.club_id)
    let requesterPrivateNetworkId = null;
    if (requesterPrivateNetwork && requesterPrivateNetwork.hasOwnProperty('id')) {
        requesterPrivateNetworkId = requesterPrivateNetwork.id
    } else if (requesterPrivateNetwork && requesterPrivateNetwork.hasOwnProperty('private_network_id')) {
        requesterPrivateNetworkId = requesterPrivateNetwork.private_network_id
    }

    const response = user_array.filter(user => {
        if (user.visible_to_favorite_clubs) {
            // If the club id is there and the user restricted clubs includes that club
            const restricted_clubs = user.restricted_clubs.filter(club => club)
            if (club_id && restricted_clubs.includes(club_id)) {
                const filteredClubs = user.favourite_clubs.filter(club => club && userClubIds.includes(club))
                if (!filteredClubs.length) {
                    return false;
                }
            }
        }

        if ((user.pace && !pace.includes((user.pace).toLowerCase())) || !user.pace) {
            return false;
        }
        if ((user.gender && gender !== 'both' && (user.gender).toLowerCase() !== gender) || !user.gender) {
            return false;
        }
        if (!all_ages && (user.age < min_age || user.age > max_age)) {
            return false;
        }
        if ((user.handicap && !handicap.includes(user.handicap)) || !user.handicap) {
            return false;
        }
        if (playAsCouple && playAsCouple !== user.playAsCouple) {
            return false;
        }
        if ((user.englishFluency && !englishFluency.includes((user.englishFluency).toLowerCase())) || !user.englishFluency) {
            return false;
        }

        // ********** Checking on the basis of TG, PN or TG + PN members ***********
        if (requesterPrivateNetworkId && !requesterVisibleToPublic &&
            requesterPrivateNetworkId !== user.private_network) {
            /* 
            * User is only a member of Private Network
            * No consideration of tier is required in this case
            */
            return false;
        } else {
            /* 
            * User is a member of TG and/or Private Network
            */
            const hostClubTier = user?.clubLowerTierVisibility || clubTier;
            console.log("hostClubTier=>",hostClubTier);
            
            if (sendToPrivateNetwork) {
                /* 
                * User is willing to send request to private network only
                */

                if (user.private_network && requesterPrivateNetworkId === user.private_network) {
                    return true;
                } else {
                    return false;
                }
            } else {
                /* 
                * User is willing to send request to both private network and TG members
                */

                if (requesterPrivateNetworkId && requesterVisibleToPublic) {
                    if ((user.private_network && requesterPrivateNetworkId === user.private_network) ||
                        (user.visibleToPublic && hostClubTier >= requesterTier)
                    ) {
                        return true;
                    }
                    if (!user.private_network && hostClubTier >= requesterTier) {
                        // To check if the user's tier is greater than the requester's tier and user is not a PN member
                        return true;
                    }
                    return false;
                } else if (!requesterPrivateNetworkId) {

                    if (user.private_network && !user.visibleToPublic) {
                        // User should not be a member of PN alone
                        return false;
                    } else {
                        if (hostClubTier >= requesterTier) {
                            return true;
                        }
                        return false;
                    }
                }
            }
        }

        return true;

    })
    return response;
}

//     let {
//         pace = ["fast", "average", "leisure"],
//         gender = "both",
//         all_ages = true,
//         max_age = null,
//         min_age = null,
//         handicap = ["< 5", "5-10", "> 10"],
//         playAsCouple = false,
//         englishFluency = ["native", "fluent", "understandable", "basic"],
//         sendToPrivateNetwork = null
//     } = filters;

//     const { tier: requesterTier, private_network: requesterPrivateNetwork, visibleToPublic: requesterVisibleToPublic } = user_data;
//     const userClubIds = user_data.clubs.map(club => club.id || club.club_id)
//     let requesterPrivateNetworkId = null;
//     if (requesterPrivateNetwork && requesterPrivateNetwork.hasOwnProperty('id')) {
//         requesterPrivateNetworkId = requesterPrivateNetwork.id
//     } else if (requesterPrivateNetwork && requesterPrivateNetwork.hasOwnProperty('private_network_id')) {
//         requesterPrivateNetworkId = requesterPrivateNetwork.private_network_id
//     }

//     const response = user_array.filter(user => {
//         if (user.visible_to_favorite_clubs) {
//             // If the club id is there and the user restricted clubs includes that club
//             const restricted_clubs = user.restricted_clubs.filter(club => club)
//             if (club_id && restricted_clubs.includes(club_id)) {
//                 const filteredClubs = user.favourite_clubs.filter(club => club && userClubIds.includes(club))
//                 if (!filteredClubs.length) {
//                     return false;
//                 }
//             }
//         }

//         if ((user.pace && !pace.includes((user.pace).toLowerCase())) || !user.pace) {
//             return false;
//         }
//         if ((user.gender && gender !== 'both' && (user.gender).toLowerCase() !== gender) || !user.gender) {
//             return false;
//         }
//         if (!all_ages && (user.age < min_age || user.age > max_age)) {
//             return false;
//         }
//         if ((user.handicap && !handicap.includes(user.handicap)) || !user.handicap) {
//             return false;
//         }
//         if (playAsCouple && playAsCouple !== user.playAsCouple) {
//             return false;
//         }
//         if ((user.englishFluency && !englishFluency.includes((user.englishFluency).toLowerCase())) || !user.englishFluency) {
//             return false;
//         }

//         // ********* Checking on the basis of TG, PN or TG + PN members **********
//         if (requesterPrivateNetworkId && !requesterVisibleToPublic &&
//             requesterPrivateNetworkId !== user?.private_network) {
//             /* 
//             * User is only a member of Private Network
//             * No consideration of tier is required in this case
//             */
//             return false;
//         } else {
//             /* 
//             * User is a member of TG and/or Private Network
//             */
//             // const hostTier = calculateLowestTier(user.tier, user.visibleToLowerTiers, user.tierVisibility);
//             const hostTier = user.tier || clubTier


//             if (sendToPrivateNetwork) {
//                 /* 
//                 * User is willing to send request to private network only
//                 */

//                 if (user?.private_network && requesterPrivateNetworkId === user?.private_network) {
//                     return true;
//                 } else {
//                     return false;
//                 }
//             } else {
//                 /* 
//                 * User is willing to send request to both private network and TG members
//                 */

//                 if (requesterPrivateNetworkId && requesterVisibleToPublic) {
//                     if ((user?.private_network && requesterPrivateNetworkId === user?.private_network) ||
//                         (user.visibleToPublic && hostTier >= requesterTier)
//                     ) {
//                         return true;
//                     }
//                     if (!user?.private_network && hostTier >= requesterTier) {
//                         // To check if the user's tier is greater than the requester's tier and user is not a PN member
//                         return true;
//                     }
//                     return false;
//                 } else if (!requesterPrivateNetworkId) {

//                     if (user?.private_network && !user.visibleToPublic) {
//                         // User should not be a member of PN alone
//                         return false;
//                     } else {
//                         if (hostTier >= requesterTier) {
//                             return true;
//                         }
//                         return false;
//                     }
//                 }
//             }
//         }

//         return true;

//     })
//     return response;
// }

function calculateLowestTier(tier, visibleToLowerTiers, tierVisibility) {
    if (!visibleToLowerTiers) {
        return tier;
    } else {
        if (tierVisibility.length) {
            return Math.max(...tierVisibility, tier);
        }
        return tier;
    }
}

// function calculateLowestTier(tier, visibleToLowerTiers, tierVisibility) {
//     if (!visibleToLowerTiers) {
//         return tier;
//     } else {
//         if (tierVisibility.length) {
//             return Math.max(...tierVisibility);
//         }
//         return tier;
//     }
// }

export function checkPaymentMethodValidity(paymentMethod, setErrors) {
    console.log('checkPaymentMethodValidity paymentMethod', paymentMethod);
    if (paymentMethod.type)
        switch (paymentMethod.type) {
            case 'digital':
                if (!paymentMethod?.digitalPaymentType) {
                    setErrors({ digitalPaymentType: `Please select your Payment method` })
                    return false
                } else if (!paymentMethod?.digitalPaymentHandle) {
                    setErrors({ digitalPaymentHandle: `Please enter your ${paymentMethod.digitalPaymentType} handle` })
                    return false
                }
                else return true
            case 'other':
                if (!paymentMethod?.otherText) {
                    setErrors({ otherText: 'Field is Mandatory' })
                    return false
                } else return true

            case 'pay by credit card at pro shop':
                if (!paymentMethod?.otherText) {
                    setErrors({ otherText: 'Field is Mandatory' })
                    return false
                } else return true
            default: return true
        }
    else {
        setErrors({ type: 'Errors..' })
        return false
    }
}