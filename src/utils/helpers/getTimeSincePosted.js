import moment from 'moment'

export default function getTimeSincePosted(post, setTimeSincePosted) {
    // Check if post and created_at are valid
    if (!post || !post.created_at) {
        setTimeSincePosted('');
        return;
    }

    try {
        var now = moment(new Date()); //todays date
        var createdAt = moment(post.created_at); // another date

        if (moment.duration(now.diff(createdAt)).asDays() < 1) {
            setTimeSincePosted(createdAt.fromNow())
        } else {
            setTimeSincePosted(createdAt.format('Do MMMM YYYY'))
        }
    } catch (error) {
        console.warn('Error formatting time since posted:', error);
        setTimeSincePosted('');
    }
}
